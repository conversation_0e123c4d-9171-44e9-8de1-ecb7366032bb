globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/pms/dashboard/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/_component/GlobalKeyboardShortcuts.tsx":{"*":{"id":"(ssr)/./app/_component/GlobalKeyboardShortcuts.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/sonner.tsx":{"*":{"id":"(ssr)/./components/ui/sonner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/user/tracker/manage.tsx":{"*":{"id":"(ssr)/./app/user/tracker/manage.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/user/trackSheets/ManageTrackSheet.tsx":{"*":{"id":"(ssr)/./app/user/trackSheets/ManageTrackSheet.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/pms/management_services/page.tsx":{"*":{"id":"(ssr)/./app/pms/management_services/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/pms/manage_tickets/TicketProvider.tsx":{"*":{"id":"(ssr)/./app/pms/manage_tickets/TicketProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/sidebar/Sidebar.tsx":{"*":{"id":"(ssr)/./components/sidebar/Sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/ui/sidebar.tsx":{"*":{"id":"(ssr)/./components/ui/sidebar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/pms/manage_invoice_files/InvoiceFilesBoard.tsx":{"*":{"id":"(ssr)/./app/pms/manage_invoice_files/InvoiceFilesBoard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/pms/manage_tickets/ManageTickets.tsx":{"*":{"id":"(ssr)/./app/pms/manage_tickets/ManageTickets.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./lib/permissionWrapper.ts":{"*":{"id":"(ssr)/./lib/permissionWrapper.ts","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(auth)/admin/ImageLogin.tsx":{"*":{"id":"(ssr)/./app/(auth)/admin/ImageLogin.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/(auth)/admin/Login.tsx":{"*":{"id":"(ssr)/./app/(auth)/admin/Login.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/pms/addupdate_custom_fields/AddCustomField.tsx":{"*":{"id":"(ssr)/./app/pms/addupdate_custom_fields/AddCustomField.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/pms/addupdate_custom_fields/ViewCustomFields.tsx":{"*":{"id":"(ssr)/./app/pms/addupdate_custom_fields/ViewCustomFields.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/pms/manage_client/addClient.tsx":{"*":{"id":"(ssr)/./app/pms/manage_client/addClient.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/pms/manage_client/ViewClient.tsx":{"*":{"id":"(ssr)/./app/pms/manage_client/ViewClient.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\PMS\\pms-moon\\client\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\PMS\\pms-moon\\client\\app\\_component\\GlobalKeyboardShortcuts.tsx":{"id":"(app-pages-browser)/./app/_component/GlobalKeyboardShortcuts.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\PMS\\pms-moon\\client\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\PMS\\pms-moon\\client\\components\\ui\\sonner.tsx":{"id":"(app-pages-browser)/./components/ui/sonner.tsx","name":"*","chunks":["app/pms/layout","static/chunks/app/pms/layout.js"],"async":false},"D:\\PMS\\pms-moon\\client\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\PMS\\pms-moon\\client\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"D:\\PMS\\pms-moon\\client\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"D:\\PMS\\pms-moon\\client\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\PMS\\pms-moon\\client\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\PMS\\pms-moon\\client\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\PMS\\pms-moon\\client\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\PMS\\pms-moon\\client\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\PMS\\pms-moon\\client\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\PMS\\pms-moon\\client\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\PMS\\pms-moon\\client\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\PMS\\pms-moon\\client\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\PMS\\pms-moon\\client\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\PMS\\pms-moon\\client\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\PMS\\pms-moon\\client\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\PMS\\pms-moon\\client\\app\\user\\tracker\\manage.tsx":{"id":"(app-pages-browser)/./app/user/tracker/manage.tsx","name":"*","chunks":[],"async":false},"D:\\PMS\\pms-moon\\client\\app\\user\\trackSheets\\ManageTrackSheet.tsx":{"id":"(app-pages-browser)/./app/user/trackSheets/ManageTrackSheet.tsx","name":"*","chunks":[],"async":false},"D:\\PMS\\pms-moon\\client\\app\\pms\\management_services\\page.tsx":{"id":"(app-pages-browser)/./app/pms/management_services/page.tsx","name":"*","chunks":[],"async":false},"D:\\PMS\\pms-moon\\client\\app\\pms\\manage_tickets\\TicketProvider.tsx":{"id":"(app-pages-browser)/./app/pms/manage_tickets/TicketProvider.tsx","name":"*","chunks":["app/pms/layout","static/chunks/app/pms/layout.js"],"async":false},"D:\\PMS\\pms-moon\\client\\components\\sidebar\\Sidebar.tsx":{"id":"(app-pages-browser)/./components/sidebar/Sidebar.tsx","name":"*","chunks":["app/pms/layout","static/chunks/app/pms/layout.js"],"async":false},"D:\\PMS\\pms-moon\\client\\components\\ui\\sidebar.tsx":{"id":"(app-pages-browser)/./components/ui/sidebar.tsx","name":"*","chunks":["app/pms/layout","static/chunks/app/pms/layout.js"],"async":false},"D:\\PMS\\pms-moon\\client\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\pms\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\pms\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/pms/layout","static/chunks/app/pms/layout.js"],"async":false},"D:\\PMS\\pms-moon\\client\\app\\pms\\manage_invoice_files\\InvoiceFilesBoard.tsx":{"id":"(app-pages-browser)/./app/pms/manage_invoice_files/InvoiceFilesBoard.tsx","name":"*","chunks":[],"async":false},"D:\\PMS\\pms-moon\\client\\app\\pms\\manage_tickets\\ManageTickets.tsx":{"id":"(app-pages-browser)/./app/pms/manage_tickets/ManageTickets.tsx","name":"*","chunks":[],"async":false},"D:\\PMS\\pms-moon\\client\\lib\\permissionWrapper.ts":{"id":"(app-pages-browser)/./lib/permissionWrapper.ts","name":"*","chunks":[],"async":false},"D:\\PMS\\pms-moon\\client\\app\\(auth)\\admin\\ImageLogin.tsx":{"id":"(app-pages-browser)/./app/(auth)/admin/ImageLogin.tsx","name":"*","chunks":[],"async":false},"D:\\PMS\\pms-moon\\client\\app\\(auth)\\admin\\Login.tsx":{"id":"(app-pages-browser)/./app/(auth)/admin/Login.tsx","name":"*","chunks":[],"async":false},"D:\\PMS\\pms-moon\\client\\app\\pms\\addupdate_custom_fields\\AddCustomField.tsx":{"id":"(app-pages-browser)/./app/pms/addupdate_custom_fields/AddCustomField.tsx","name":"*","chunks":[],"async":false},"D:\\PMS\\pms-moon\\client\\app\\pms\\addupdate_custom_fields\\ViewCustomFields.tsx":{"id":"(app-pages-browser)/./app/pms/addupdate_custom_fields/ViewCustomFields.tsx","name":"*","chunks":[],"async":false},"D:\\PMS\\pms-moon\\client\\app\\pms\\manage_client\\addClient.tsx":{"id":"(app-pages-browser)/./app/pms/manage_client/addClient.tsx","name":"*","chunks":[],"async":false},"D:\\PMS\\pms-moon\\client\\app\\pms\\manage_client\\ViewClient.tsx":{"id":"(app-pages-browser)/./app/pms/manage_client/ViewClient.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\PMS\\pms-moon\\client\\":[],"D:\\PMS\\pms-moon\\client\\app\\page":[],"D:\\PMS\\pms-moon\\client\\app\\layout":["static/css/app/layout.css"],"D:\\PMS\\pms-moon\\client\\app\\not-found":[],"D:\\PMS\\pms-moon\\client\\app\\pms\\layout":["static/css/app/pms/layout.css"],"D:\\PMS\\pms-moon\\client\\app\\pms\\dashboard\\page":[]}}