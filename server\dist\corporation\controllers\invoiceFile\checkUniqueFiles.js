"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkBulkUniqueInvoiceFiles = exports.findDuplicateInvoiceFiles = void 0;
const helpers_1 = require("../../../utils/helpers");
const findDuplicateInvoiceFiles = async (carrierId, date, files) => {
    return prisma.invoiceFile.findMany({
        where: {
            carrierId,
            date: new Date(date),
            OR: files.map((file) => ({
                fileName: file.fileName.trim(),
                noOfPages: file.noOfPages,
            })),
        },
        select: {
            fileName: true,
            noOfPages: true,
            date: true,
        },
    });
};
exports.findDuplicateInvoiceFiles = findDuplicateInvoiceFiles;
const checkBulkUniqueInvoiceFiles = async (req, res) => {
    try {
        const { carrier, date, files } = req.body;
        if (!carrier || !date || !Array.isArray(files) || files.length === 0) {
            return res.status(400).json({ message: "Carrier, date, and files are required" });
        }
        const duplicates = await (0, exports.findDuplicateInvoiceFiles)(Number(carrier), date, files.map((file) => ({
            fileName: file.fileName,
            noOfPages: file.noOfPages,
        })));
        if (duplicates.length > 0) {
            const conflictList = duplicates.map((file) => `File "${file.fileName}" with ${file.noOfPages} pages on ${file.date.toISOString().split("T")[0]}`);
            return res.status(409).json({
                message: "Some invoice files already exist with same carrier, date, file name, and page count.",
                duplicates: conflictList,
            });
        }
        return res.status(200).json({ message: "All files are unique. You may proceed." });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.checkBulkUniqueInvoiceFiles = checkBulkUniqueInvoiceFiles;
//# sourceMappingURL=checkUniqueFiles.js.map