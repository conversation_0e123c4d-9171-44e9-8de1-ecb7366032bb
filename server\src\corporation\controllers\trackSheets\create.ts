import { handleError } from "../../../utils/helpers";
import { TrackSheetWarningService } from "../../../services/trackSheetWarningService";

// Helper function to parse DD/MM/YYYY format dates
const parseDDMMYYYYDate = (dateString: string): Date | null => {
  if (!dateString || dateString.trim() === '') return null;

  // Handle DD/MM/YYYY format
  const parts = dateString.split('/');
  if (parts.length === 3) {
    const day = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10);
    const year = parseInt(parts[2], 10);

    // Validate the parts
    if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 1900) {
      // Create date in UTC to avoid timezone issues
      return new Date(Date.UTC(year, month - 1, day));
    }
  }

  // Fallback to regular Date parsing if format doesn't match
  const fallbackDate = new Date(dateString);
  return isNaN(fallbackDate.getTime()) ? null : fallbackDate;
};

export const trackSheetService = async (trackSheetData: any) => {
  try {
    const createdTrackSheet = await prisma.trackSheets.create({
      data: trackSheetData,
    });
    return createdTrackSheet;
  } catch (error) {
    throw error;
  }
};

export const customFieldsService = async (trackSheetId, customFieldsData) => {
  try {
    const mappings = [];

    for (const customField of customFieldsData) {
      const mapping = await prisma.trackSheetCustomFieldMapping.create({
        data: {
          tracksheetId: trackSheetId,
          customFieldId: customField.id,
          value: customField.value,
        },
      });
      mappings.push(mapping);
    }
    return mappings;
  } catch (error) {
    throw error;
  }
};

export const generateAndSaveWarnings = async (trackSheetId, entryFields, preparedTrackSheetData) => {
  return await TrackSheetWarningService.generateAndSaveWarnings(
    trackSheetId,
    {
      freightTerm: entryFields.freightTerm,
      shipperAddressType: entryFields.shipperAddressType,
      consigneeAddressType: entryFields.consigneeAddressType,
      billToAddressType: entryFields.billToAddressType,
    },
    entryFields.enteredBy || preparedTrackSheetData.enteredBy
  );
};

export const createTrackSheets = async (req, res) => {
  try {
    const createdTrackSheets = [];
    const allWarnings = [];
//
    // Check if we need to update invoice file status
    const updateInvoiceFileStatus = async (fileId: string) => {
      if (fileId) {
        await prisma.invoiceFile.update({
          where: { id: fileId },
          data: { status: 'done' }
        });
      }
    };

    for (const entry of req.body.entries) {
      const { customFields, fileId, ...entryFields } = entry;
//
      // Update invoice file status if fileId is provided and finalInvoice is true
      if (fileId && entryFields.finalInvoice === true) {
        await updateInvoiceFileStatus(fileId);
      }

      const preparedTrackSheetData = {
        ...entryFields,
        clientId: Number(req.body.clientId),
        carrierId: entryFields.carrierId ? Number(entryFields.carrierId) : null,
        qtyShipped: entryFields.qtyShipped
          ? Number(entryFields.qtyShipped)
          : null,
        invoiceTotal: entryFields.invoiceTotal
          ? Number(entryFields.invoiceTotal)
          : null,
        invoiceDate: parseDDMMYYYYDate(entryFields.invoiceDate),
        receivedDate: parseDDMMYYYYDate(entryFields.receivedDate),
        shipmentDate: parseDDMMYYYYDate(entryFields.shipmentDate),
        billToClient: entryFields.billToClient === "yes" ? true : entryFields.billToClient === "no" ? false : null,
        docAvailable: Array.isArray(entryFields.docAvailable)
          ? entryFields.docAvailable.join(",")
          : entryFields.docAvailable || null,
        // New freight and address type fields
        freightTerm: entryFields.freightTerm || 'LEGACY',
        shipperAddressType: entryFields.shipperAddressType || 'LEGACY',
        consigneeAddressType: entryFields.consigneeAddressType || 'LEGACY',
        billToAddressType: entryFields.billToAddressType || 'LEGACY',
        shipperAddress: entryFields.shipperAddress || null,
        consigneeAddress: entryFields.consigneeAddress || null,
        billToAddress: entryFields.billToAddress || null,
        finalInvoice: entryFields.finalInvoice || false,
      };

      Object.keys(preparedTrackSheetData).forEach((key) => {
        if (preparedTrackSheetData[key] === undefined) {
          delete preparedTrackSheetData[key];
        }
      });

      const createdTrackSheet = await trackSheetService(preparedTrackSheetData);
      createdTrackSheets.push(createdTrackSheet);

      if (
        customFields &&
        Array.isArray(customFields) &&
        customFields.length > 0
      ) {
        await customFieldsService(createdTrackSheet.id, customFields);
      }

      // Generate and save warnings, collect them for response
      const trackSheetWarnings = await generateAndSaveWarnings(createdTrackSheet.id, entryFields, preparedTrackSheetData);
      
      // Add warnings to the collection with tracksheet reference
      if (trackSheetWarnings && trackSheetWarnings.length > 0) {
        allWarnings.push({
          trackSheetId: createdTrackSheet.id,
          warnings: trackSheetWarnings
        });
      }
    }

    return res.status(201).json({
      success: true,
      message: `${createdTrackSheets.length} TrackSheet(s) created successfully`,
      data: createdTrackSheets,
      systemGeneratedWarnings: allWarnings,
    });
  } catch (error) {
    console.error("Error creating tracksheet:", error);
    return handleError(res, error);
  }
};