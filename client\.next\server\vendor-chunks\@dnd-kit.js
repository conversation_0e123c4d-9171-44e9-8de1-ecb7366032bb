"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@dnd-kit";
exports.ids = ["vendor-chunks/@dnd-kit"];
exports.modules = {

/***/ "(ssr)/./node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HiddenText: () => (/* binding */ HiddenText),\n/* harmony export */   LiveRegion: () => (/* binding */ LiveRegion),\n/* harmony export */   useAnnouncement: () => (/* binding */ useAnnouncement)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nconst hiddenStyles = {\n  display: 'none'\n};\nfunction HiddenText(_ref) {\n  let {\n    id,\n    value\n  } = _ref;\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    id: id,\n    style: hiddenStyles\n  }, value);\n}\n\nfunction LiveRegion(_ref) {\n  let {\n    id,\n    announcement,\n    ariaLiveType = \"assertive\"\n  } = _ref;\n  // Hide element visually but keep it readable by screen readers\n  const visuallyHidden = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(100%)',\n    whiteSpace: 'nowrap'\n  };\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    id: id,\n    style: visuallyHidden,\n    role: \"status\",\n    \"aria-live\": ariaLiveType,\n    \"aria-atomic\": true\n  }, announcement);\n}\n\nfunction useAnnouncement() {\n  const [announcement, setAnnouncement] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n  const announce = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(value => {\n    if (value != null) {\n      setAnnouncement(value);\n    }\n  }, []);\n  return {\n    announce,\n    announcement\n  };\n}\n\n\n//# sourceMappingURL=accessibility.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGRuZC1raXQvYWNjZXNzaWJpbGl0eS9kaXN0L2FjY2Vzc2liaWxpdHkuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXFEOztBQUVyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSixTQUFTLDBEQUFtQjtBQUM1QjtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUywwREFBbUI7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBLDBDQUEwQywrQ0FBUTtBQUNsRCxtQkFBbUIsa0RBQVc7QUFDOUI7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW1EO0FBQ25EIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2xpZW50Ly4vbm9kZV9tb2R1bGVzL0BkbmQta2l0L2FjY2Vzc2liaWxpdHkvZGlzdC9hY2Nlc3NpYmlsaXR5LmVzbS5qcz8zNzU0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XG5cbmNvbnN0IGhpZGRlblN0eWxlcyA9IHtcbiAgZGlzcGxheTogJ25vbmUnXG59O1xuZnVuY3Rpb24gSGlkZGVuVGV4dChfcmVmKSB7XG4gIGxldCB7XG4gICAgaWQsXG4gICAgdmFsdWVcbiAgfSA9IF9yZWY7XG4gIHJldHVybiBSZWFjdC5jcmVhdGVFbGVtZW50KFwiZGl2XCIsIHtcbiAgICBpZDogaWQsXG4gICAgc3R5bGU6IGhpZGRlblN0eWxlc1xuICB9LCB2YWx1ZSk7XG59XG5cbmZ1bmN0aW9uIExpdmVSZWdpb24oX3JlZikge1xuICBsZXQge1xuICAgIGlkLFxuICAgIGFubm91bmNlbWVudCxcbiAgICBhcmlhTGl2ZVR5cGUgPSBcImFzc2VydGl2ZVwiXG4gIH0gPSBfcmVmO1xuICAvLyBIaWRlIGVsZW1lbnQgdmlzdWFsbHkgYnV0IGtlZXAgaXQgcmVhZGFibGUgYnkgc2NyZWVuIHJlYWRlcnNcbiAgY29uc3QgdmlzdWFsbHlIaWRkZW4gPSB7XG4gICAgcG9zaXRpb246ICdmaXhlZCcsXG4gICAgdG9wOiAwLFxuICAgIGxlZnQ6IDAsXG4gICAgd2lkdGg6IDEsXG4gICAgaGVpZ2h0OiAxLFxuICAgIG1hcmdpbjogLTEsXG4gICAgYm9yZGVyOiAwLFxuICAgIHBhZGRpbmc6IDAsXG4gICAgb3ZlcmZsb3c6ICdoaWRkZW4nLFxuICAgIGNsaXA6ICdyZWN0KDAgMCAwIDApJyxcbiAgICBjbGlwUGF0aDogJ2luc2V0KDEwMCUpJyxcbiAgICB3aGl0ZVNwYWNlOiAnbm93cmFwJ1xuICB9O1xuICByZXR1cm4gUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAgaWQ6IGlkLFxuICAgIHN0eWxlOiB2aXN1YWxseUhpZGRlbixcbiAgICByb2xlOiBcInN0YXR1c1wiLFxuICAgIFwiYXJpYS1saXZlXCI6IGFyaWFMaXZlVHlwZSxcbiAgICBcImFyaWEtYXRvbWljXCI6IHRydWVcbiAgfSwgYW5ub3VuY2VtZW50KTtcbn1cblxuZnVuY3Rpb24gdXNlQW5ub3VuY2VtZW50KCkge1xuICBjb25zdCBbYW5ub3VuY2VtZW50LCBzZXRBbm5vdW5jZW1lbnRdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBhbm5vdW5jZSA9IHVzZUNhbGxiYWNrKHZhbHVlID0+IHtcbiAgICBpZiAodmFsdWUgIT0gbnVsbCkge1xuICAgICAgc2V0QW5ub3VuY2VtZW50KHZhbHVlKTtcbiAgICB9XG4gIH0sIFtdKTtcbiAgcmV0dXJuIHtcbiAgICBhbm5vdW5jZSxcbiAgICBhbm5vdW5jZW1lbnRcbiAgfTtcbn1cblxuZXhwb3J0IHsgSGlkZGVuVGV4dCwgTGl2ZVJlZ2lvbiwgdXNlQW5ub3VuY2VtZW50IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1hY2Nlc3NpYmlsaXR5LmVzbS5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@dnd-kit/core/dist/core.esm.js":
/*!*****************************************************!*\
  !*** ./node_modules/@dnd-kit/core/dist/core.esm.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoScrollActivator: () => (/* binding */ AutoScrollActivator),\n/* harmony export */   DndContext: () => (/* binding */ DndContext),\n/* harmony export */   DragOverlay: () => (/* binding */ DragOverlay),\n/* harmony export */   KeyboardCode: () => (/* binding */ KeyboardCode),\n/* harmony export */   KeyboardSensor: () => (/* binding */ KeyboardSensor),\n/* harmony export */   MeasuringFrequency: () => (/* binding */ MeasuringFrequency),\n/* harmony export */   MeasuringStrategy: () => (/* binding */ MeasuringStrategy),\n/* harmony export */   MouseSensor: () => (/* binding */ MouseSensor),\n/* harmony export */   PointerSensor: () => (/* binding */ PointerSensor),\n/* harmony export */   TouchSensor: () => (/* binding */ TouchSensor),\n/* harmony export */   TraversalOrder: () => (/* binding */ TraversalOrder),\n/* harmony export */   applyModifiers: () => (/* binding */ applyModifiers),\n/* harmony export */   closestCenter: () => (/* binding */ closestCenter),\n/* harmony export */   closestCorners: () => (/* binding */ closestCorners),\n/* harmony export */   defaultAnnouncements: () => (/* binding */ defaultAnnouncements),\n/* harmony export */   defaultCoordinates: () => (/* binding */ defaultCoordinates),\n/* harmony export */   defaultDropAnimation: () => (/* binding */ defaultDropAnimationConfiguration),\n/* harmony export */   defaultDropAnimationSideEffects: () => (/* binding */ defaultDropAnimationSideEffects),\n/* harmony export */   defaultKeyboardCoordinateGetter: () => (/* binding */ defaultKeyboardCoordinateGetter),\n/* harmony export */   defaultScreenReaderInstructions: () => (/* binding */ defaultScreenReaderInstructions),\n/* harmony export */   getClientRect: () => (/* binding */ getClientRect),\n/* harmony export */   getFirstCollision: () => (/* binding */ getFirstCollision),\n/* harmony export */   getScrollableAncestors: () => (/* binding */ getScrollableAncestors),\n/* harmony export */   pointerWithin: () => (/* binding */ pointerWithin),\n/* harmony export */   rectIntersection: () => (/* binding */ rectIntersection),\n/* harmony export */   useDndContext: () => (/* binding */ useDndContext),\n/* harmony export */   useDndMonitor: () => (/* binding */ useDndMonitor),\n/* harmony export */   useDraggable: () => (/* binding */ useDraggable),\n/* harmony export */   useDroppable: () => (/* binding */ useDroppable),\n/* harmony export */   useSensor: () => (/* binding */ useSensor),\n/* harmony export */   useSensors: () => (/* binding */ useSensors)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/utilities */ \"(ssr)/./node_modules/@dnd-kit/utilities/dist/utilities.esm.js\");\n/* harmony import */ var _dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @dnd-kit/accessibility */ \"(ssr)/./node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js\");\n\n\n\n\n\nconst DndMonitorContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\n\nfunction useDndMonitor(listener) {\n  const registerListener = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(DndMonitorContext);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!registerListener) {\n      throw new Error('useDndMonitor must be used within a children of <DndContext>');\n    }\n\n    const unsubscribe = registerListener(listener);\n    return unsubscribe;\n  }, [listener, registerListener]);\n}\n\nfunction useDndMonitorProvider() {\n  const [listeners] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new Set());\n  const registerListener = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(listener => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  }, [listeners]);\n  const dispatch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(_ref => {\n    let {\n      type,\n      event\n    } = _ref;\n    listeners.forEach(listener => {\n      var _listener$type;\n\n      return (_listener$type = listener[type]) == null ? void 0 : _listener$type.call(listener, event);\n    });\n  }, [listeners]);\n  return [dispatch, registerListener];\n}\n\nconst defaultScreenReaderInstructions = {\n  draggable: \"\\n    To pick up a draggable item, press the space bar.\\n    While dragging, use the arrow keys to move the item.\\n    Press space again to drop the item in its new position, or press escape to cancel.\\n  \"\n};\nconst defaultAnnouncements = {\n  onDragStart(_ref) {\n    let {\n      active\n    } = _ref;\n    return \"Picked up draggable item \" + active.id + \".\";\n  },\n\n  onDragOver(_ref2) {\n    let {\n      active,\n      over\n    } = _ref2;\n\n    if (over) {\n      return \"Draggable item \" + active.id + \" was moved over droppable area \" + over.id + \".\";\n    }\n\n    return \"Draggable item \" + active.id + \" is no longer over a droppable area.\";\n  },\n\n  onDragEnd(_ref3) {\n    let {\n      active,\n      over\n    } = _ref3;\n\n    if (over) {\n      return \"Draggable item \" + active.id + \" was dropped over droppable area \" + over.id;\n    }\n\n    return \"Draggable item \" + active.id + \" was dropped.\";\n  },\n\n  onDragCancel(_ref4) {\n    let {\n      active\n    } = _ref4;\n    return \"Dragging was cancelled. Draggable item \" + active.id + \" was dropped.\";\n  }\n\n};\n\nfunction Accessibility(_ref) {\n  let {\n    announcements = defaultAnnouncements,\n    container,\n    hiddenTextDescribedById,\n    screenReaderInstructions = defaultScreenReaderInstructions\n  } = _ref;\n  const {\n    announce,\n    announcement\n  } = (0,_dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__.useAnnouncement)();\n  const liveRegionId = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(\"DndLiveRegion\");\n  const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    setMounted(true);\n  }, []);\n  useDndMonitor((0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    onDragStart(_ref2) {\n      let {\n        active\n      } = _ref2;\n      announce(announcements.onDragStart({\n        active\n      }));\n    },\n\n    onDragMove(_ref3) {\n      let {\n        active,\n        over\n      } = _ref3;\n\n      if (announcements.onDragMove) {\n        announce(announcements.onDragMove({\n          active,\n          over\n        }));\n      }\n    },\n\n    onDragOver(_ref4) {\n      let {\n        active,\n        over\n      } = _ref4;\n      announce(announcements.onDragOver({\n        active,\n        over\n      }));\n    },\n\n    onDragEnd(_ref5) {\n      let {\n        active,\n        over\n      } = _ref5;\n      announce(announcements.onDragEnd({\n        active,\n        over\n      }));\n    },\n\n    onDragCancel(_ref6) {\n      let {\n        active,\n        over\n      } = _ref6;\n      announce(announcements.onDragCancel({\n        active,\n        over\n      }));\n    }\n\n  }), [announce, announcements]));\n\n  if (!mounted) {\n    return null;\n  }\n\n  const markup = react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__.HiddenText, {\n    id: hiddenTextDescribedById,\n    value: screenReaderInstructions.draggable\n  }), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_dnd_kit_accessibility__WEBPACK_IMPORTED_MODULE_3__.LiveRegion, {\n    id: liveRegionId,\n    announcement: announcement\n  }));\n  return container ? (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(markup, container) : markup;\n}\n\nvar Action;\n\n(function (Action) {\n  Action[\"DragStart\"] = \"dragStart\";\n  Action[\"DragMove\"] = \"dragMove\";\n  Action[\"DragEnd\"] = \"dragEnd\";\n  Action[\"DragCancel\"] = \"dragCancel\";\n  Action[\"DragOver\"] = \"dragOver\";\n  Action[\"RegisterDroppable\"] = \"registerDroppable\";\n  Action[\"SetDroppableDisabled\"] = \"setDroppableDisabled\";\n  Action[\"UnregisterDroppable\"] = \"unregisterDroppable\";\n})(Action || (Action = {}));\n\nfunction noop() {}\n\nfunction useSensor(sensor, options) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    sensor,\n    options: options != null ? options : {}\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [sensor, options]);\n}\n\nfunction useSensors() {\n  for (var _len = arguments.length, sensors = new Array(_len), _key = 0; _key < _len; _key++) {\n    sensors[_key] = arguments[_key];\n  }\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => [...sensors].filter(sensor => sensor != null), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...sensors]);\n}\n\nconst defaultCoordinates = /*#__PURE__*/Object.freeze({\n  x: 0,\n  y: 0\n});\n\n/**\r\n * Returns the distance between two points\r\n */\nfunction distanceBetween(p1, p2) {\n  return Math.sqrt(Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2));\n}\n\nfunction getRelativeTransformOrigin(event, rect) {\n  const eventCoordinates = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(event);\n\n  if (!eventCoordinates) {\n    return '0 0';\n  }\n\n  const transformOrigin = {\n    x: (eventCoordinates.x - rect.left) / rect.width * 100,\n    y: (eventCoordinates.y - rect.top) / rect.height * 100\n  };\n  return transformOrigin.x + \"% \" + transformOrigin.y + \"%\";\n}\n\n/**\r\n * Sort collisions from smallest to greatest value\r\n */\nfunction sortCollisionsAsc(_ref, _ref2) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref2;\n  return a - b;\n}\n/**\r\n * Sort collisions from greatest to smallest value\r\n */\n\nfunction sortCollisionsDesc(_ref3, _ref4) {\n  let {\n    data: {\n      value: a\n    }\n  } = _ref3;\n  let {\n    data: {\n      value: b\n    }\n  } = _ref4;\n  return b - a;\n}\n/**\r\n * Returns the coordinates of the corners of a given rectangle:\r\n * [TopLeft {x, y}, TopRight {x, y}, BottomLeft {x, y}, BottomRight {x, y}]\r\n */\n\nfunction cornersOfRectangle(_ref5) {\n  let {\n    left,\n    top,\n    height,\n    width\n  } = _ref5;\n  return [{\n    x: left,\n    y: top\n  }, {\n    x: left + width,\n    y: top\n  }, {\n    x: left,\n    y: top + height\n  }, {\n    x: left + width,\n    y: top + height\n  }];\n}\nfunction getFirstCollision(collisions, property) {\n  if (!collisions || collisions.length === 0) {\n    return null;\n  }\n\n  const [firstCollision] = collisions;\n  return property ? firstCollision[property] : firstCollision;\n}\n\n/**\r\n * Returns the coordinates of the center of a given ClientRect\r\n */\n\nfunction centerOfRectangle(rect, left, top) {\n  if (left === void 0) {\n    left = rect.left;\n  }\n\n  if (top === void 0) {\n    top = rect.top;\n  }\n\n  return {\n    x: left + rect.width * 0.5,\n    y: top + rect.height * 0.5\n  };\n}\n/**\r\n * Returns the closest rectangles from an array of rectangles to the center of a given\r\n * rectangle.\r\n */\n\n\nconst closestCenter = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const centerRect = centerOfRectangle(collisionRect, collisionRect.left, collisionRect.top);\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const distBetween = distanceBetween(centerOfRectangle(rect), centerRect);\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: distBetween\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\n/**\r\n * Returns the closest rectangles from an array of rectangles to the corners of\r\n * another rectangle.\r\n */\n\nconst closestCorners = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const corners = cornersOfRectangle(collisionRect);\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const rectCorners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner, index) => {\n        return accumulator + distanceBetween(rectCorners[index], corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\n/**\r\n * Returns the intersecting rectangle area between two rectangles\r\n */\n\nfunction getIntersectionRatio(entry, target) {\n  const top = Math.max(target.top, entry.top);\n  const left = Math.max(target.left, entry.left);\n  const right = Math.min(target.left + target.width, entry.left + entry.width);\n  const bottom = Math.min(target.top + target.height, entry.top + entry.height);\n  const width = right - left;\n  const height = bottom - top;\n\n  if (left < right && top < bottom) {\n    const targetArea = target.width * target.height;\n    const entryArea = entry.width * entry.height;\n    const intersectionArea = width * height;\n    const intersectionRatio = intersectionArea / (targetArea + entryArea - intersectionArea);\n    return Number(intersectionRatio.toFixed(4));\n  } // Rectangles do not overlap, or overlap has an area of zero (edge/corner overlap)\n\n\n  return 0;\n}\n/**\r\n * Returns the rectangles that has the greatest intersection area with a given\r\n * rectangle in an array of rectangles.\r\n */\n\nconst rectIntersection = _ref => {\n  let {\n    collisionRect,\n    droppableRects,\n    droppableContainers\n  } = _ref;\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect) {\n      const intersectionRatio = getIntersectionRatio(rect, collisionRect);\n\n      if (intersectionRatio > 0) {\n        collisions.push({\n          id,\n          data: {\n            droppableContainer,\n            value: intersectionRatio\n          }\n        });\n      }\n    }\n  }\n\n  return collisions.sort(sortCollisionsDesc);\n};\n\n/**\r\n * Check if a given point is contained within a bounding rectangle\r\n */\n\nfunction isPointWithinRect(point, rect) {\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = rect;\n  return top <= point.y && point.y <= bottom && left <= point.x && point.x <= right;\n}\n/**\r\n * Returns the rectangles that the pointer is hovering over\r\n */\n\n\nconst pointerWithin = _ref => {\n  let {\n    droppableContainers,\n    droppableRects,\n    pointerCoordinates\n  } = _ref;\n\n  if (!pointerCoordinates) {\n    return [];\n  }\n\n  const collisions = [];\n\n  for (const droppableContainer of droppableContainers) {\n    const {\n      id\n    } = droppableContainer;\n    const rect = droppableRects.get(id);\n\n    if (rect && isPointWithinRect(pointerCoordinates, rect)) {\n      /* There may be more than a single rectangle intersecting\r\n       * with the pointer coordinates. In order to sort the\r\n       * colliding rectangles, we measure the distance between\r\n       * the pointer and the corners of the intersecting rectangle\r\n       */\n      const corners = cornersOfRectangle(rect);\n      const distances = corners.reduce((accumulator, corner) => {\n        return accumulator + distanceBetween(pointerCoordinates, corner);\n      }, 0);\n      const effectiveDistance = Number((distances / 4).toFixed(4));\n      collisions.push({\n        id,\n        data: {\n          droppableContainer,\n          value: effectiveDistance\n        }\n      });\n    }\n  }\n\n  return collisions.sort(sortCollisionsAsc);\n};\n\nfunction adjustScale(transform, rect1, rect2) {\n  return { ...transform,\n    scaleX: rect1 && rect2 ? rect1.width / rect2.width : 1,\n    scaleY: rect1 && rect2 ? rect1.height / rect2.height : 1\n  };\n}\n\nfunction getRectDelta(rect1, rect2) {\n  return rect1 && rect2 ? {\n    x: rect1.left - rect2.left,\n    y: rect1.top - rect2.top\n  } : defaultCoordinates;\n}\n\nfunction createRectAdjustmentFn(modifier) {\n  return function adjustClientRect(rect) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n\n    return adjustments.reduce((acc, adjustment) => ({ ...acc,\n      top: acc.top + modifier * adjustment.y,\n      bottom: acc.bottom + modifier * adjustment.y,\n      left: acc.left + modifier * adjustment.x,\n      right: acc.right + modifier * adjustment.x\n    }), { ...rect\n    });\n  };\n}\nconst getAdjustedRect = /*#__PURE__*/createRectAdjustmentFn(1);\n\nfunction parseTransform(transform) {\n  if (transform.startsWith('matrix3d(')) {\n    const transformArray = transform.slice(9, -1).split(/, /);\n    return {\n      x: +transformArray[12],\n      y: +transformArray[13],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[5]\n    };\n  } else if (transform.startsWith('matrix(')) {\n    const transformArray = transform.slice(7, -1).split(/, /);\n    return {\n      x: +transformArray[4],\n      y: +transformArray[5],\n      scaleX: +transformArray[0],\n      scaleY: +transformArray[3]\n    };\n  }\n\n  return null;\n}\n\nfunction inverseTransform(rect, transform, transformOrigin) {\n  const parsedTransform = parseTransform(transform);\n\n  if (!parsedTransform) {\n    return rect;\n  }\n\n  const {\n    scaleX,\n    scaleY,\n    x: translateX,\n    y: translateY\n  } = parsedTransform;\n  const x = rect.left - translateX - (1 - scaleX) * parseFloat(transformOrigin);\n  const y = rect.top - translateY - (1 - scaleY) * parseFloat(transformOrigin.slice(transformOrigin.indexOf(' ') + 1));\n  const w = scaleX ? rect.width / scaleX : rect.width;\n  const h = scaleY ? rect.height / scaleY : rect.height;\n  return {\n    width: w,\n    height: h,\n    top: y,\n    right: x + w,\n    bottom: y + h,\n    left: x\n  };\n}\n\nconst defaultOptions = {\n  ignoreTransform: false\n};\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n */\n\nfunction getClientRect(element, options) {\n  if (options === void 0) {\n    options = defaultOptions;\n  }\n\n  let rect = element.getBoundingClientRect();\n\n  if (options.ignoreTransform) {\n    const {\n      transform,\n      transformOrigin\n    } = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(element).getComputedStyle(element);\n\n    if (transform) {\n      rect = inverseTransform(rect, transform, transformOrigin);\n    }\n  }\n\n  const {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  } = rect;\n  return {\n    top,\n    left,\n    width,\n    height,\n    bottom,\n    right\n  };\n}\n/**\r\n * Returns the bounding client rect of an element relative to the viewport.\r\n *\r\n * @remarks\r\n * The ClientRect returned by this method does not take into account transforms\r\n * applied to the element it measures.\r\n *\r\n */\n\nfunction getTransformAgnosticClientRect(element) {\n  return getClientRect(element, {\n    ignoreTransform: true\n  });\n}\n\nfunction getWindowClientRect(element) {\n  const width = element.innerWidth;\n  const height = element.innerHeight;\n  return {\n    top: 0,\n    left: 0,\n    right: width,\n    bottom: height,\n    width,\n    height\n  };\n}\n\nfunction isFixed(node, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(node).getComputedStyle(node);\n  }\n\n  return computedStyle.position === 'fixed';\n}\n\nfunction isScrollable(element, computedStyle) {\n  if (computedStyle === void 0) {\n    computedStyle = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(element).getComputedStyle(element);\n  }\n\n  const overflowRegex = /(auto|scroll|overlay)/;\n  const properties = ['overflow', 'overflowX', 'overflowY'];\n  return properties.some(property => {\n    const value = computedStyle[property];\n    return typeof value === 'string' ? overflowRegex.test(value) : false;\n  });\n}\n\nfunction getScrollableAncestors(element, limit) {\n  const scrollParents = [];\n\n  function findScrollableAncestors(node) {\n    if (limit != null && scrollParents.length >= limit) {\n      return scrollParents;\n    }\n\n    if (!node) {\n      return scrollParents;\n    }\n\n    if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isDocument)(node) && node.scrollingElement != null && !scrollParents.includes(node.scrollingElement)) {\n      scrollParents.push(node.scrollingElement);\n      return scrollParents;\n    }\n\n    if (!(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(node) || (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isSVGElement)(node)) {\n      return scrollParents;\n    }\n\n    if (scrollParents.includes(node)) {\n      return scrollParents;\n    }\n\n    const computedStyle = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(element).getComputedStyle(node);\n\n    if (node !== element) {\n      if (isScrollable(node, computedStyle)) {\n        scrollParents.push(node);\n      }\n    }\n\n    if (isFixed(node, computedStyle)) {\n      return scrollParents;\n    }\n\n    return findScrollableAncestors(node.parentNode);\n  }\n\n  if (!element) {\n    return scrollParents;\n  }\n\n  return findScrollableAncestors(element);\n}\nfunction getFirstScrollableAncestor(node) {\n  const [firstScrollableAncestor] = getScrollableAncestors(node, 1);\n  return firstScrollableAncestor != null ? firstScrollableAncestor : null;\n}\n\nfunction getScrollableElement(element) {\n  if (!_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.canUseDOM || !element) {\n    return null;\n  }\n\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isWindow)(element)) {\n    return element;\n  }\n\n  if (!(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isNode)(element)) {\n    return null;\n  }\n\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isDocument)(element) || element === (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(element).scrollingElement) {\n    return window;\n  }\n\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(element)) {\n    return element;\n  }\n\n  return null;\n}\n\nfunction getScrollXCoordinate(element) {\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isWindow)(element)) {\n    return element.scrollX;\n  }\n\n  return element.scrollLeft;\n}\nfunction getScrollYCoordinate(element) {\n  if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isWindow)(element)) {\n    return element.scrollY;\n  }\n\n  return element.scrollTop;\n}\nfunction getScrollCoordinates(element) {\n  return {\n    x: getScrollXCoordinate(element),\n    y: getScrollYCoordinate(element)\n  };\n}\n\nvar Direction;\n\n(function (Direction) {\n  Direction[Direction[\"Forward\"] = 1] = \"Forward\";\n  Direction[Direction[\"Backward\"] = -1] = \"Backward\";\n})(Direction || (Direction = {}));\n\nfunction isDocumentScrollingElement(element) {\n  if (!_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.canUseDOM || !element) {\n    return false;\n  }\n\n  return element === document.scrollingElement;\n}\n\nfunction getScrollPosition(scrollingContainer) {\n  const minScroll = {\n    x: 0,\n    y: 0\n  };\n  const dimensions = isDocumentScrollingElement(scrollingContainer) ? {\n    height: window.innerHeight,\n    width: window.innerWidth\n  } : {\n    height: scrollingContainer.clientHeight,\n    width: scrollingContainer.clientWidth\n  };\n  const maxScroll = {\n    x: scrollingContainer.scrollWidth - dimensions.width,\n    y: scrollingContainer.scrollHeight - dimensions.height\n  };\n  const isTop = scrollingContainer.scrollTop <= minScroll.y;\n  const isLeft = scrollingContainer.scrollLeft <= minScroll.x;\n  const isBottom = scrollingContainer.scrollTop >= maxScroll.y;\n  const isRight = scrollingContainer.scrollLeft >= maxScroll.x;\n  return {\n    isTop,\n    isLeft,\n    isBottom,\n    isRight,\n    maxScroll,\n    minScroll\n  };\n}\n\nconst defaultThreshold = {\n  x: 0.2,\n  y: 0.2\n};\nfunction getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, _ref, acceleration, thresholdPercentage) {\n  let {\n    top,\n    left,\n    right,\n    bottom\n  } = _ref;\n\n  if (acceleration === void 0) {\n    acceleration = 10;\n  }\n\n  if (thresholdPercentage === void 0) {\n    thresholdPercentage = defaultThreshold;\n  }\n\n  const {\n    isTop,\n    isBottom,\n    isLeft,\n    isRight\n  } = getScrollPosition(scrollContainer);\n  const direction = {\n    x: 0,\n    y: 0\n  };\n  const speed = {\n    x: 0,\n    y: 0\n  };\n  const threshold = {\n    height: scrollContainerRect.height * thresholdPercentage.y,\n    width: scrollContainerRect.width * thresholdPercentage.x\n  };\n\n  if (!isTop && top <= scrollContainerRect.top + threshold.height) {\n    // Scroll Up\n    direction.y = Direction.Backward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.top + threshold.height - top) / threshold.height);\n  } else if (!isBottom && bottom >= scrollContainerRect.bottom - threshold.height) {\n    // Scroll Down\n    direction.y = Direction.Forward;\n    speed.y = acceleration * Math.abs((scrollContainerRect.bottom - threshold.height - bottom) / threshold.height);\n  }\n\n  if (!isRight && right >= scrollContainerRect.right - threshold.width) {\n    // Scroll Right\n    direction.x = Direction.Forward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.right - threshold.width - right) / threshold.width);\n  } else if (!isLeft && left <= scrollContainerRect.left + threshold.width) {\n    // Scroll Left\n    direction.x = Direction.Backward;\n    speed.x = acceleration * Math.abs((scrollContainerRect.left + threshold.width - left) / threshold.width);\n  }\n\n  return {\n    direction,\n    speed\n  };\n}\n\nfunction getScrollElementRect(element) {\n  if (element === document.scrollingElement) {\n    const {\n      innerWidth,\n      innerHeight\n    } = window;\n    return {\n      top: 0,\n      left: 0,\n      right: innerWidth,\n      bottom: innerHeight,\n      width: innerWidth,\n      height: innerHeight\n    };\n  }\n\n  const {\n    top,\n    left,\n    right,\n    bottom\n  } = element.getBoundingClientRect();\n  return {\n    top,\n    left,\n    right,\n    bottom,\n    width: element.clientWidth,\n    height: element.clientHeight\n  };\n}\n\nfunction getScrollOffsets(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(acc, getScrollCoordinates(node));\n  }, defaultCoordinates);\n}\nfunction getScrollXOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollXCoordinate(node);\n  }, 0);\n}\nfunction getScrollYOffset(scrollableAncestors) {\n  return scrollableAncestors.reduce((acc, node) => {\n    return acc + getScrollYCoordinate(node);\n  }, 0);\n}\n\nfunction scrollIntoViewIfNeeded(element, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n\n  if (!element) {\n    return;\n  }\n\n  const {\n    top,\n    left,\n    bottom,\n    right\n  } = measure(element);\n  const firstScrollableAncestor = getFirstScrollableAncestor(element);\n\n  if (!firstScrollableAncestor) {\n    return;\n  }\n\n  if (bottom <= 0 || right <= 0 || top >= window.innerHeight || left >= window.innerWidth) {\n    element.scrollIntoView({\n      block: 'center',\n      inline: 'center'\n    });\n  }\n}\n\nconst properties = [['x', ['left', 'right'], getScrollXOffset], ['y', ['top', 'bottom'], getScrollYOffset]];\nclass Rect {\n  constructor(rect, element) {\n    this.rect = void 0;\n    this.width = void 0;\n    this.height = void 0;\n    this.top = void 0;\n    this.bottom = void 0;\n    this.right = void 0;\n    this.left = void 0;\n    const scrollableAncestors = getScrollableAncestors(element);\n    const scrollOffsets = getScrollOffsets(scrollableAncestors);\n    this.rect = { ...rect\n    };\n    this.width = rect.width;\n    this.height = rect.height;\n\n    for (const [axis, keys, getScrollOffset] of properties) {\n      for (const key of keys) {\n        Object.defineProperty(this, key, {\n          get: () => {\n            const currentOffsets = getScrollOffset(scrollableAncestors);\n            const scrollOffsetsDeltla = scrollOffsets[axis] - currentOffsets;\n            return this.rect[key] + scrollOffsetsDeltla;\n          },\n          enumerable: true\n        });\n      }\n    }\n\n    Object.defineProperty(this, 'rect', {\n      enumerable: false\n    });\n  }\n\n}\n\nclass Listeners {\n  constructor(target) {\n    this.target = void 0;\n    this.listeners = [];\n\n    this.removeAll = () => {\n      this.listeners.forEach(listener => {\n        var _this$target;\n\n        return (_this$target = this.target) == null ? void 0 : _this$target.removeEventListener(...listener);\n      });\n    };\n\n    this.target = target;\n  }\n\n  add(eventName, handler, options) {\n    var _this$target2;\n\n    (_this$target2 = this.target) == null ? void 0 : _this$target2.addEventListener(eventName, handler, options);\n    this.listeners.push([eventName, handler, options]);\n  }\n\n}\n\nfunction getEventListenerTarget(target) {\n  // If the `event.target` element is removed from the document events will still be targeted\n  // at it, and hence won't always bubble up to the window or document anymore.\n  // If there is any risk of an element being removed while it is being dragged,\n  // the best practice is to attach the event listeners directly to the target.\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget\n  const {\n    EventTarget\n  } = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(target);\n  return target instanceof EventTarget ? target : (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(target);\n}\n\nfunction hasExceededDistance(delta, measurement) {\n  const dx = Math.abs(delta.x);\n  const dy = Math.abs(delta.y);\n\n  if (typeof measurement === 'number') {\n    return Math.sqrt(dx ** 2 + dy ** 2) > measurement;\n  }\n\n  if ('x' in measurement && 'y' in measurement) {\n    return dx > measurement.x && dy > measurement.y;\n  }\n\n  if ('x' in measurement) {\n    return dx > measurement.x;\n  }\n\n  if ('y' in measurement) {\n    return dy > measurement.y;\n  }\n\n  return false;\n}\n\nvar EventName;\n\n(function (EventName) {\n  EventName[\"Click\"] = \"click\";\n  EventName[\"DragStart\"] = \"dragstart\";\n  EventName[\"Keydown\"] = \"keydown\";\n  EventName[\"ContextMenu\"] = \"contextmenu\";\n  EventName[\"Resize\"] = \"resize\";\n  EventName[\"SelectionChange\"] = \"selectionchange\";\n  EventName[\"VisibilityChange\"] = \"visibilitychange\";\n})(EventName || (EventName = {}));\n\nfunction preventDefault(event) {\n  event.preventDefault();\n}\nfunction stopPropagation(event) {\n  event.stopPropagation();\n}\n\nvar KeyboardCode;\n\n(function (KeyboardCode) {\n  KeyboardCode[\"Space\"] = \"Space\";\n  KeyboardCode[\"Down\"] = \"ArrowDown\";\n  KeyboardCode[\"Right\"] = \"ArrowRight\";\n  KeyboardCode[\"Left\"] = \"ArrowLeft\";\n  KeyboardCode[\"Up\"] = \"ArrowUp\";\n  KeyboardCode[\"Esc\"] = \"Escape\";\n  KeyboardCode[\"Enter\"] = \"Enter\";\n  KeyboardCode[\"Tab\"] = \"Tab\";\n})(KeyboardCode || (KeyboardCode = {}));\n\nconst defaultKeyboardCodes = {\n  start: [KeyboardCode.Space, KeyboardCode.Enter],\n  cancel: [KeyboardCode.Esc],\n  end: [KeyboardCode.Space, KeyboardCode.Enter, KeyboardCode.Tab]\n};\nconst defaultKeyboardCoordinateGetter = (event, _ref) => {\n  let {\n    currentCoordinates\n  } = _ref;\n\n  switch (event.code) {\n    case KeyboardCode.Right:\n      return { ...currentCoordinates,\n        x: currentCoordinates.x + 25\n      };\n\n    case KeyboardCode.Left:\n      return { ...currentCoordinates,\n        x: currentCoordinates.x - 25\n      };\n\n    case KeyboardCode.Down:\n      return { ...currentCoordinates,\n        y: currentCoordinates.y + 25\n      };\n\n    case KeyboardCode.Up:\n      return { ...currentCoordinates,\n        y: currentCoordinates.y - 25\n      };\n  }\n\n  return undefined;\n};\n\nclass KeyboardSensor {\n  constructor(props) {\n    this.props = void 0;\n    this.autoScrollEnabled = false;\n    this.referenceCoordinates = void 0;\n    this.listeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    const {\n      event: {\n        target\n      }\n    } = props;\n    this.props = props;\n    this.listeners = new Listeners((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(target));\n    this.windowListeners = new Listeners((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(target));\n    this.handleKeyDown = this.handleKeyDown.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.attach();\n  }\n\n  attach() {\n    this.handleStart();\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    setTimeout(() => this.listeners.add(EventName.Keydown, this.handleKeyDown));\n  }\n\n  handleStart() {\n    const {\n      activeNode,\n      onStart\n    } = this.props;\n    const node = activeNode.node.current;\n\n    if (node) {\n      scrollIntoViewIfNeeded(node);\n    }\n\n    onStart(defaultCoordinates);\n  }\n\n  handleKeyDown(event) {\n    if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(event)) {\n      const {\n        active,\n        context,\n        options\n      } = this.props;\n      const {\n        keyboardCodes = defaultKeyboardCodes,\n        coordinateGetter = defaultKeyboardCoordinateGetter,\n        scrollBehavior = 'smooth'\n      } = options;\n      const {\n        code\n      } = event;\n\n      if (keyboardCodes.end.includes(code)) {\n        this.handleEnd(event);\n        return;\n      }\n\n      if (keyboardCodes.cancel.includes(code)) {\n        this.handleCancel(event);\n        return;\n      }\n\n      const {\n        collisionRect\n      } = context.current;\n      const currentCoordinates = collisionRect ? {\n        x: collisionRect.left,\n        y: collisionRect.top\n      } : defaultCoordinates;\n\n      if (!this.referenceCoordinates) {\n        this.referenceCoordinates = currentCoordinates;\n      }\n\n      const newCoordinates = coordinateGetter(event, {\n        active,\n        context: context.current,\n        currentCoordinates\n      });\n\n      if (newCoordinates) {\n        const coordinatesDelta = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(newCoordinates, currentCoordinates);\n        const scrollDelta = {\n          x: 0,\n          y: 0\n        };\n        const {\n          scrollableAncestors\n        } = context.current;\n\n        for (const scrollContainer of scrollableAncestors) {\n          const direction = event.code;\n          const {\n            isTop,\n            isRight,\n            isLeft,\n            isBottom,\n            maxScroll,\n            minScroll\n          } = getScrollPosition(scrollContainer);\n          const scrollElementRect = getScrollElementRect(scrollContainer);\n          const clampedCoordinates = {\n            x: Math.min(direction === KeyboardCode.Right ? scrollElementRect.right - scrollElementRect.width / 2 : scrollElementRect.right, Math.max(direction === KeyboardCode.Right ? scrollElementRect.left : scrollElementRect.left + scrollElementRect.width / 2, newCoordinates.x)),\n            y: Math.min(direction === KeyboardCode.Down ? scrollElementRect.bottom - scrollElementRect.height / 2 : scrollElementRect.bottom, Math.max(direction === KeyboardCode.Down ? scrollElementRect.top : scrollElementRect.top + scrollElementRect.height / 2, newCoordinates.y))\n          };\n          const canScrollX = direction === KeyboardCode.Right && !isRight || direction === KeyboardCode.Left && !isLeft;\n          const canScrollY = direction === KeyboardCode.Down && !isBottom || direction === KeyboardCode.Up && !isTop;\n\n          if (canScrollX && clampedCoordinates.x !== newCoordinates.x) {\n            const newScrollCoordinates = scrollContainer.scrollLeft + coordinatesDelta.x;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Right && newScrollCoordinates <= maxScroll.x || direction === KeyboardCode.Left && newScrollCoordinates >= minScroll.x;\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.y) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                left: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.x = scrollContainer.scrollLeft - newScrollCoordinates;\n            } else {\n              scrollDelta.x = direction === KeyboardCode.Right ? scrollContainer.scrollLeft - maxScroll.x : scrollContainer.scrollLeft - minScroll.x;\n            }\n\n            if (scrollDelta.x) {\n              scrollContainer.scrollBy({\n                left: -scrollDelta.x,\n                behavior: scrollBehavior\n              });\n            }\n\n            break;\n          } else if (canScrollY && clampedCoordinates.y !== newCoordinates.y) {\n            const newScrollCoordinates = scrollContainer.scrollTop + coordinatesDelta.y;\n            const canScrollToNewCoordinates = direction === KeyboardCode.Down && newScrollCoordinates <= maxScroll.y || direction === KeyboardCode.Up && newScrollCoordinates >= minScroll.y;\n\n            if (canScrollToNewCoordinates && !coordinatesDelta.x) {\n              // We don't need to update coordinates, the scroll adjustment alone will trigger\n              // logic to auto-detect the new container we are over\n              scrollContainer.scrollTo({\n                top: newScrollCoordinates,\n                behavior: scrollBehavior\n              });\n              return;\n            }\n\n            if (canScrollToNewCoordinates) {\n              scrollDelta.y = scrollContainer.scrollTop - newScrollCoordinates;\n            } else {\n              scrollDelta.y = direction === KeyboardCode.Down ? scrollContainer.scrollTop - maxScroll.y : scrollContainer.scrollTop - minScroll.y;\n            }\n\n            if (scrollDelta.y) {\n              scrollContainer.scrollBy({\n                top: -scrollDelta.y,\n                behavior: scrollBehavior\n              });\n            }\n\n            break;\n          }\n        }\n\n        this.handleMove(event, (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(newCoordinates, this.referenceCoordinates), scrollDelta));\n      }\n    }\n  }\n\n  handleMove(event, coordinates) {\n    const {\n      onMove\n    } = this.props;\n    event.preventDefault();\n    onMove(coordinates);\n  }\n\n  handleEnd(event) {\n    const {\n      onEnd\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onEnd();\n  }\n\n  handleCancel(event) {\n    const {\n      onCancel\n    } = this.props;\n    event.preventDefault();\n    this.detach();\n    onCancel();\n  }\n\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll();\n  }\n\n}\nKeyboardSensor.activators = [{\n  eventName: 'onKeyDown',\n  handler: (event, _ref, _ref2) => {\n    let {\n      keyboardCodes = defaultKeyboardCodes,\n      onActivation\n    } = _ref;\n    let {\n      active\n    } = _ref2;\n    const {\n      code\n    } = event.nativeEvent;\n\n    if (keyboardCodes.start.includes(code)) {\n      const activator = active.activatorNode.current;\n\n      if (activator && event.target !== activator) {\n        return false;\n      }\n\n      event.preventDefault();\n      onActivation == null ? void 0 : onActivation({\n        event: event.nativeEvent\n      });\n      return true;\n    }\n\n    return false;\n  }\n}];\n\nfunction isDistanceConstraint(constraint) {\n  return Boolean(constraint && 'distance' in constraint);\n}\n\nfunction isDelayConstraint(constraint) {\n  return Boolean(constraint && 'delay' in constraint);\n}\n\nclass AbstractPointerSensor {\n  constructor(props, events, listenerTarget) {\n    var _getEventCoordinates;\n\n    if (listenerTarget === void 0) {\n      listenerTarget = getEventListenerTarget(props.event.target);\n    }\n\n    this.props = void 0;\n    this.events = void 0;\n    this.autoScrollEnabled = true;\n    this.document = void 0;\n    this.activated = false;\n    this.initialCoordinates = void 0;\n    this.timeoutId = null;\n    this.listeners = void 0;\n    this.documentListeners = void 0;\n    this.windowListeners = void 0;\n    this.props = props;\n    this.events = events;\n    const {\n      event\n    } = props;\n    const {\n      target\n    } = event;\n    this.props = props;\n    this.events = events;\n    this.document = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(target);\n    this.documentListeners = new Listeners(this.document);\n    this.listeners = new Listeners(listenerTarget);\n    this.windowListeners = new Listeners((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(target));\n    this.initialCoordinates = (_getEventCoordinates = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(event)) != null ? _getEventCoordinates : defaultCoordinates;\n    this.handleStart = this.handleStart.bind(this);\n    this.handleMove = this.handleMove.bind(this);\n    this.handleEnd = this.handleEnd.bind(this);\n    this.handleCancel = this.handleCancel.bind(this);\n    this.handleKeydown = this.handleKeydown.bind(this);\n    this.removeTextSelection = this.removeTextSelection.bind(this);\n    this.attach();\n  }\n\n  attach() {\n    const {\n      events,\n      props: {\n        options: {\n          activationConstraint,\n          bypassActivationConstraint\n        }\n      }\n    } = this;\n    this.listeners.add(events.move.name, this.handleMove, {\n      passive: false\n    });\n    this.listeners.add(events.end.name, this.handleEnd);\n\n    if (events.cancel) {\n      this.listeners.add(events.cancel.name, this.handleCancel);\n    }\n\n    this.windowListeners.add(EventName.Resize, this.handleCancel);\n    this.windowListeners.add(EventName.DragStart, preventDefault);\n    this.windowListeners.add(EventName.VisibilityChange, this.handleCancel);\n    this.windowListeners.add(EventName.ContextMenu, preventDefault);\n    this.documentListeners.add(EventName.Keydown, this.handleKeydown);\n\n    if (activationConstraint) {\n      if (bypassActivationConstraint != null && bypassActivationConstraint({\n        event: this.props.event,\n        activeNode: this.props.activeNode,\n        options: this.props.options\n      })) {\n        return this.handleStart();\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        this.timeoutId = setTimeout(this.handleStart, activationConstraint.delay);\n        this.handlePending(activationConstraint);\n        return;\n      }\n\n      if (isDistanceConstraint(activationConstraint)) {\n        this.handlePending(activationConstraint);\n        return;\n      }\n    }\n\n    this.handleStart();\n  }\n\n  detach() {\n    this.listeners.removeAll();\n    this.windowListeners.removeAll(); // Wait until the next event loop before removing document listeners\n    // This is necessary because we listen for `click` and `selection` events on the document\n\n    setTimeout(this.documentListeners.removeAll, 50);\n\n    if (this.timeoutId !== null) {\n      clearTimeout(this.timeoutId);\n      this.timeoutId = null;\n    }\n  }\n\n  handlePending(constraint, offset) {\n    const {\n      active,\n      onPending\n    } = this.props;\n    onPending(active, constraint, this.initialCoordinates, offset);\n  }\n\n  handleStart() {\n    const {\n      initialCoordinates\n    } = this;\n    const {\n      onStart\n    } = this.props;\n\n    if (initialCoordinates) {\n      this.activated = true; // Stop propagation of click events once activation constraints are met\n\n      this.documentListeners.add(EventName.Click, stopPropagation, {\n        capture: true\n      }); // Remove any text selection from the document\n\n      this.removeTextSelection(); // Prevent further text selection while dragging\n\n      this.documentListeners.add(EventName.SelectionChange, this.removeTextSelection);\n      onStart(initialCoordinates);\n    }\n  }\n\n  handleMove(event) {\n    var _getEventCoordinates2;\n\n    const {\n      activated,\n      initialCoordinates,\n      props\n    } = this;\n    const {\n      onMove,\n      options: {\n        activationConstraint\n      }\n    } = props;\n\n    if (!initialCoordinates) {\n      return;\n    }\n\n    const coordinates = (_getEventCoordinates2 = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(event)) != null ? _getEventCoordinates2 : defaultCoordinates;\n    const delta = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(initialCoordinates, coordinates); // Constraint validation\n\n    if (!activated && activationConstraint) {\n      if (isDistanceConstraint(activationConstraint)) {\n        if (activationConstraint.tolerance != null && hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n\n        if (hasExceededDistance(delta, activationConstraint.distance)) {\n          return this.handleStart();\n        }\n      }\n\n      if (isDelayConstraint(activationConstraint)) {\n        if (hasExceededDistance(delta, activationConstraint.tolerance)) {\n          return this.handleCancel();\n        }\n      }\n\n      this.handlePending(activationConstraint, delta);\n      return;\n    }\n\n    if (event.cancelable) {\n      event.preventDefault();\n    }\n\n    onMove(coordinates);\n  }\n\n  handleEnd() {\n    const {\n      onAbort,\n      onEnd\n    } = this.props;\n    this.detach();\n\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n\n    onEnd();\n  }\n\n  handleCancel() {\n    const {\n      onAbort,\n      onCancel\n    } = this.props;\n    this.detach();\n\n    if (!this.activated) {\n      onAbort(this.props.active);\n    }\n\n    onCancel();\n  }\n\n  handleKeydown(event) {\n    if (event.code === KeyboardCode.Esc) {\n      this.handleCancel();\n    }\n  }\n\n  removeTextSelection() {\n    var _this$document$getSel;\n\n    (_this$document$getSel = this.document.getSelection()) == null ? void 0 : _this$document$getSel.removeAllRanges();\n  }\n\n}\n\nconst events = {\n  cancel: {\n    name: 'pointercancel'\n  },\n  move: {\n    name: 'pointermove'\n  },\n  end: {\n    name: 'pointerup'\n  }\n};\nclass PointerSensor extends AbstractPointerSensor {\n  constructor(props) {\n    const {\n      event\n    } = props; // Pointer events stop firing if the target is unmounted while dragging\n    // Therefore we attach listeners to the owner document instead\n\n    const listenerTarget = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(event.target);\n    super(props, events, listenerTarget);\n  }\n\n}\nPointerSensor.activators = [{\n  eventName: 'onPointerDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n\n    if (!event.isPrimary || event.button !== 0) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nconst events$1 = {\n  move: {\n    name: 'mousemove'\n  },\n  end: {\n    name: 'mouseup'\n  }\n};\nvar MouseButton;\n\n(function (MouseButton) {\n  MouseButton[MouseButton[\"RightClick\"] = 2] = \"RightClick\";\n})(MouseButton || (MouseButton = {}));\n\nclass MouseSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$1, (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getOwnerDocument)(props.event.target));\n  }\n\n}\nMouseSensor.activators = [{\n  eventName: 'onMouseDown',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n\n    if (event.button === MouseButton.RightClick) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nconst events$2 = {\n  cancel: {\n    name: 'touchcancel'\n  },\n  move: {\n    name: 'touchmove'\n  },\n  end: {\n    name: 'touchend'\n  }\n};\nclass TouchSensor extends AbstractPointerSensor {\n  constructor(props) {\n    super(props, events$2);\n  }\n\n  static setup() {\n    // Adding a non-capture and non-passive `touchmove` listener in order\n    // to force `event.preventDefault()` calls to work in dynamically added\n    // touchmove event handlers. This is required for iOS Safari.\n    window.addEventListener(events$2.move.name, noop, {\n      capture: false,\n      passive: false\n    });\n    return function teardown() {\n      window.removeEventListener(events$2.move.name, noop);\n    }; // We create a new handler because the teardown function of another sensor\n    // could remove our event listener if we use a referentially equal listener.\n\n    function noop() {}\n  }\n\n}\nTouchSensor.activators = [{\n  eventName: 'onTouchStart',\n  handler: (_ref, _ref2) => {\n    let {\n      nativeEvent: event\n    } = _ref;\n    let {\n      onActivation\n    } = _ref2;\n    const {\n      touches\n    } = event;\n\n    if (touches.length > 1) {\n      return false;\n    }\n\n    onActivation == null ? void 0 : onActivation({\n      event\n    });\n    return true;\n  }\n}];\n\nvar AutoScrollActivator;\n\n(function (AutoScrollActivator) {\n  AutoScrollActivator[AutoScrollActivator[\"Pointer\"] = 0] = \"Pointer\";\n  AutoScrollActivator[AutoScrollActivator[\"DraggableRect\"] = 1] = \"DraggableRect\";\n})(AutoScrollActivator || (AutoScrollActivator = {}));\n\nvar TraversalOrder;\n\n(function (TraversalOrder) {\n  TraversalOrder[TraversalOrder[\"TreeOrder\"] = 0] = \"TreeOrder\";\n  TraversalOrder[TraversalOrder[\"ReversedTreeOrder\"] = 1] = \"ReversedTreeOrder\";\n})(TraversalOrder || (TraversalOrder = {}));\n\nfunction useAutoScroller(_ref) {\n  let {\n    acceleration,\n    activator = AutoScrollActivator.Pointer,\n    canScroll,\n    draggingRect,\n    enabled,\n    interval = 5,\n    order = TraversalOrder.TreeOrder,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    delta,\n    threshold\n  } = _ref;\n  const scrollIntent = useScrollIntent({\n    delta,\n    disabled: !enabled\n  });\n  const [setAutoScrollInterval, clearAutoScrollInterval] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useInterval)();\n  const scrollSpeed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    x: 0,\n    y: 0\n  });\n  const scrollDirection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    x: 0,\n    y: 0\n  });\n  const rect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    switch (activator) {\n      case AutoScrollActivator.Pointer:\n        return pointerCoordinates ? {\n          top: pointerCoordinates.y,\n          bottom: pointerCoordinates.y,\n          left: pointerCoordinates.x,\n          right: pointerCoordinates.x\n        } : null;\n\n      case AutoScrollActivator.DraggableRect:\n        return draggingRect;\n    }\n  }, [activator, draggingRect, pointerCoordinates]);\n  const scrollContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const autoScroll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const scrollContainer = scrollContainerRef.current;\n\n    if (!scrollContainer) {\n      return;\n    }\n\n    const scrollLeft = scrollSpeed.current.x * scrollDirection.current.x;\n    const scrollTop = scrollSpeed.current.y * scrollDirection.current.y;\n    scrollContainer.scrollBy(scrollLeft, scrollTop);\n  }, []);\n  const sortedScrollableAncestors = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => order === TraversalOrder.TreeOrder ? [...scrollableAncestors].reverse() : scrollableAncestors, [order, scrollableAncestors]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!enabled || !scrollableAncestors.length || !rect) {\n      clearAutoScrollInterval();\n      return;\n    }\n\n    for (const scrollContainer of sortedScrollableAncestors) {\n      if ((canScroll == null ? void 0 : canScroll(scrollContainer)) === false) {\n        continue;\n      }\n\n      const index = scrollableAncestors.indexOf(scrollContainer);\n      const scrollContainerRect = scrollableAncestorRects[index];\n\n      if (!scrollContainerRect) {\n        continue;\n      }\n\n      const {\n        direction,\n        speed\n      } = getScrollDirectionAndSpeed(scrollContainer, scrollContainerRect, rect, acceleration, threshold);\n\n      for (const axis of ['x', 'y']) {\n        if (!scrollIntent[axis][direction[axis]]) {\n          speed[axis] = 0;\n          direction[axis] = 0;\n        }\n      }\n\n      if (speed.x > 0 || speed.y > 0) {\n        clearAutoScrollInterval();\n        scrollContainerRef.current = scrollContainer;\n        setAutoScrollInterval(autoScroll, interval);\n        scrollSpeed.current = speed;\n        scrollDirection.current = direction;\n        return;\n      }\n    }\n\n    scrollSpeed.current = {\n      x: 0,\n      y: 0\n    };\n    scrollDirection.current = {\n      x: 0,\n      y: 0\n    };\n    clearAutoScrollInterval();\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [acceleration, autoScroll, canScroll, clearAutoScrollInterval, enabled, interval, // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(rect), // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(scrollIntent), setAutoScrollInterval, scrollableAncestors, sortedScrollableAncestors, scrollableAncestorRects, // eslint-disable-next-line react-hooks/exhaustive-deps\n  JSON.stringify(threshold)]);\n}\nconst defaultScrollIntent = {\n  x: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  },\n  y: {\n    [Direction.Backward]: false,\n    [Direction.Forward]: false\n  }\n};\n\nfunction useScrollIntent(_ref2) {\n  let {\n    delta,\n    disabled\n  } = _ref2;\n  const previousDelta = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(delta);\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousIntent => {\n    if (disabled || !previousDelta || !previousIntent) {\n      // Reset scroll intent tracking when auto-scrolling is disabled\n      return defaultScrollIntent;\n    }\n\n    const direction = {\n      x: Math.sign(delta.x - previousDelta.x),\n      y: Math.sign(delta.y - previousDelta.y)\n    }; // Keep track of the user intent to scroll in each direction for both axis\n\n    return {\n      x: {\n        [Direction.Backward]: previousIntent.x[Direction.Backward] || direction.x === -1,\n        [Direction.Forward]: previousIntent.x[Direction.Forward] || direction.x === 1\n      },\n      y: {\n        [Direction.Backward]: previousIntent.y[Direction.Backward] || direction.y === -1,\n        [Direction.Forward]: previousIntent.y[Direction.Forward] || direction.y === 1\n      }\n    };\n  }, [disabled, delta, previousDelta]);\n}\n\nfunction useCachedNode(draggableNodes, id) {\n  const draggableNode = id != null ? draggableNodes.get(id) : undefined;\n  const node = draggableNode ? draggableNode.node.current : null;\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(cachedNode => {\n    var _ref;\n\n    if (id == null) {\n      return null;\n    } // In some cases, the draggable node can unmount while dragging\n    // This is the case for virtualized lists. In those situations,\n    // we fall back to the last known value for that node.\n\n\n    return (_ref = node != null ? node : cachedNode) != null ? _ref : null;\n  }, [node, id]);\n}\n\nfunction useCombineActivators(sensors, getSyntheticHandler) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => sensors.reduce((accumulator, sensor) => {\n    const {\n      sensor: Sensor\n    } = sensor;\n    const sensorActivators = Sensor.activators.map(activator => ({\n      eventName: activator.eventName,\n      handler: getSyntheticHandler(activator.handler, sensor)\n    }));\n    return [...accumulator, ...sensorActivators];\n  }, []), [sensors, getSyntheticHandler]);\n}\n\nvar MeasuringStrategy;\n\n(function (MeasuringStrategy) {\n  MeasuringStrategy[MeasuringStrategy[\"Always\"] = 0] = \"Always\";\n  MeasuringStrategy[MeasuringStrategy[\"BeforeDragging\"] = 1] = \"BeforeDragging\";\n  MeasuringStrategy[MeasuringStrategy[\"WhileDragging\"] = 2] = \"WhileDragging\";\n})(MeasuringStrategy || (MeasuringStrategy = {}));\n\nvar MeasuringFrequency;\n\n(function (MeasuringFrequency) {\n  MeasuringFrequency[\"Optimized\"] = \"optimized\";\n})(MeasuringFrequency || (MeasuringFrequency = {}));\n\nconst defaultValue = /*#__PURE__*/new Map();\nfunction useDroppableMeasuring(containers, _ref) {\n  let {\n    dragging,\n    dependencies,\n    config\n  } = _ref;\n  const [queue, setQueue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const {\n    frequency,\n    measure,\n    strategy\n  } = config;\n  const containersRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(containers);\n  const disabled = isDisabled();\n  const disabledRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(disabled);\n  const measureDroppableContainers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (ids) {\n    if (ids === void 0) {\n      ids = [];\n    }\n\n    if (disabledRef.current) {\n      return;\n    }\n\n    setQueue(value => {\n      if (value === null) {\n        return ids;\n      }\n\n      return value.concat(ids.filter(id => !value.includes(id)));\n    });\n  }, [disabledRef]);\n  const timeoutId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const droppableRects = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousValue => {\n    if (disabled && !dragging) {\n      return defaultValue;\n    }\n\n    if (!previousValue || previousValue === defaultValue || containersRef.current !== containers || queue != null) {\n      const map = new Map();\n\n      for (let container of containers) {\n        if (!container) {\n          continue;\n        }\n\n        if (queue && queue.length > 0 && !queue.includes(container.id) && container.rect.current) {\n          // This container does not need to be re-measured\n          map.set(container.id, container.rect.current);\n          continue;\n        }\n\n        const node = container.node.current;\n        const rect = node ? new Rect(measure(node), node) : null;\n        container.rect.current = rect;\n\n        if (rect) {\n          map.set(container.id, rect);\n        }\n      }\n\n      return map;\n    }\n\n    return previousValue;\n  }, [containers, queue, dragging, disabled, measure]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    containersRef.current = containers;\n  }, [containers]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) {\n      return;\n    }\n\n    measureDroppableContainers();\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [dragging, disabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (queue && queue.length > 0) {\n      setQueue(null);\n    }\n  }, //eslint-disable-next-line react-hooks/exhaustive-deps\n  [JSON.stringify(queue)]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled || typeof frequency !== 'number' || timeoutId.current !== null) {\n      return;\n    }\n\n    timeoutId.current = setTimeout(() => {\n      measureDroppableContainers();\n      timeoutId.current = null;\n    }, frequency);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [frequency, disabled, measureDroppableContainers, ...dependencies]);\n  return {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled: queue != null\n  };\n\n  function isDisabled() {\n    switch (strategy) {\n      case MeasuringStrategy.Always:\n        return false;\n\n      case MeasuringStrategy.BeforeDragging:\n        return dragging;\n\n      default:\n        return !dragging;\n    }\n  }\n}\n\nfunction useInitialValue(value, computeFn) {\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousValue => {\n    if (!value) {\n      return null;\n    }\n\n    if (previousValue) {\n      return previousValue;\n    }\n\n    return typeof computeFn === 'function' ? computeFn(value) : value;\n  }, [computeFn, value]);\n}\n\nfunction useInitialRect(node, measure) {\n  return useInitialValue(node, measure);\n}\n\n/**\r\n * Returns a new MutationObserver instance.\r\n * If `MutationObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useMutationObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleMutations = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useEvent)(callback);\n  const mutationObserver = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.MutationObserver === 'undefined') {\n      return undefined;\n    }\n\n    const {\n      MutationObserver\n    } = window;\n    return new MutationObserver(handleMutations);\n  }, [handleMutations, disabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return () => mutationObserver == null ? void 0 : mutationObserver.disconnect();\n  }, [mutationObserver]);\n  return mutationObserver;\n}\n\n/**\r\n * Returns a new ResizeObserver instance bound to the `onResize` callback.\r\n * If `ResizeObserver` is undefined in the execution environment, returns `undefined`.\r\n */\n\nfunction useResizeObserver(_ref) {\n  let {\n    callback,\n    disabled\n  } = _ref;\n  const handleResize = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useEvent)(callback);\n  const resizeObserver = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (disabled || typeof window === 'undefined' || typeof window.ResizeObserver === 'undefined') {\n      return undefined;\n    }\n\n    const {\n      ResizeObserver\n    } = window;\n    return new ResizeObserver(handleResize);\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [disabled]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return () => resizeObserver == null ? void 0 : resizeObserver.disconnect();\n  }, [resizeObserver]);\n  return resizeObserver;\n}\n\nfunction defaultMeasure(element) {\n  return new Rect(getClientRect(element), element);\n}\n\nfunction useRect(element, measure, fallbackRect) {\n  if (measure === void 0) {\n    measure = defaultMeasure;\n  }\n\n  const [rect, setRect] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n\n  function measureRect() {\n    setRect(currentRect => {\n      if (!element) {\n        return null;\n      }\n\n      if (element.isConnected === false) {\n        var _ref;\n\n        // Fall back to last rect we measured if the element is\n        // no longer connected to the DOM.\n        return (_ref = currentRect != null ? currentRect : fallbackRect) != null ? _ref : null;\n      }\n\n      const newRect = measure(element);\n\n      if (JSON.stringify(currentRect) === JSON.stringify(newRect)) {\n        return currentRect;\n      }\n\n      return newRect;\n    });\n  }\n\n  const mutationObserver = useMutationObserver({\n    callback(records) {\n      if (!element) {\n        return;\n      }\n\n      for (const record of records) {\n        const {\n          type,\n          target\n        } = record;\n\n        if (type === 'childList' && target instanceof HTMLElement && target.contains(element)) {\n          measureRect();\n          break;\n        }\n      }\n    }\n\n  });\n  const resizeObserver = useResizeObserver({\n    callback: measureRect\n  });\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    measureRect();\n\n    if (element) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(element);\n      mutationObserver == null ? void 0 : mutationObserver.observe(document.body, {\n        childList: true,\n        subtree: true\n      });\n    } else {\n      resizeObserver == null ? void 0 : resizeObserver.disconnect();\n      mutationObserver == null ? void 0 : mutationObserver.disconnect();\n    }\n  }, [element]);\n  return rect;\n}\n\nfunction useRectDelta(rect) {\n  const initialRect = useInitialValue(rect);\n  return getRectDelta(rect, initialRect);\n}\n\nconst defaultValue$1 = [];\nfunction useScrollableAncestors(node) {\n  const previousNode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(node);\n  const ancestors = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLazyMemo)(previousValue => {\n    if (!node) {\n      return defaultValue$1;\n    }\n\n    if (previousValue && previousValue !== defaultValue$1 && node && previousNode.current && node.parentNode === previousNode.current.parentNode) {\n      return previousValue;\n    }\n\n    return getScrollableAncestors(node);\n  }, [node]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    previousNode.current = node;\n  }, [node]);\n  return ancestors;\n}\n\nfunction useScrollOffsets(elements) {\n  const [scrollCoordinates, setScrollCoordinates] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const prevElements = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(elements); // To-do: Throttle the handleScroll callback\n\n  const handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(event => {\n    const scrollingElement = getScrollableElement(event.target);\n\n    if (!scrollingElement) {\n      return;\n    }\n\n    setScrollCoordinates(scrollCoordinates => {\n      if (!scrollCoordinates) {\n        return null;\n      }\n\n      scrollCoordinates.set(scrollingElement, getScrollCoordinates(scrollingElement));\n      return new Map(scrollCoordinates);\n    });\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const previousElements = prevElements.current;\n\n    if (elements !== previousElements) {\n      cleanup(previousElements);\n      const entries = elements.map(element => {\n        const scrollableElement = getScrollableElement(element);\n\n        if (scrollableElement) {\n          scrollableElement.addEventListener('scroll', handleScroll, {\n            passive: true\n          });\n          return [scrollableElement, getScrollCoordinates(scrollableElement)];\n        }\n\n        return null;\n      }).filter(entry => entry != null);\n      setScrollCoordinates(entries.length ? new Map(entries) : null);\n      prevElements.current = elements;\n    }\n\n    return () => {\n      cleanup(elements);\n      cleanup(previousElements);\n    };\n\n    function cleanup(elements) {\n      elements.forEach(element => {\n        const scrollableElement = getScrollableElement(element);\n        scrollableElement == null ? void 0 : scrollableElement.removeEventListener('scroll', handleScroll);\n      });\n    }\n  }, [handleScroll, elements]);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (elements.length) {\n      return scrollCoordinates ? Array.from(scrollCoordinates.values()).reduce((acc, coordinates) => (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(acc, coordinates), defaultCoordinates) : getScrollOffsets(elements);\n    }\n\n    return defaultCoordinates;\n  }, [elements, scrollCoordinates]);\n}\n\nfunction useScrollOffsetsDelta(scrollOffsets, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [];\n  }\n\n  const initialScrollOffsets = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    initialScrollOffsets.current = null;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  dependencies);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const hasScrollOffsets = scrollOffsets !== defaultCoordinates;\n\n    if (hasScrollOffsets && !initialScrollOffsets.current) {\n      initialScrollOffsets.current = scrollOffsets;\n    }\n\n    if (!hasScrollOffsets && initialScrollOffsets.current) {\n      initialScrollOffsets.current = null;\n    }\n  }, [scrollOffsets]);\n  return initialScrollOffsets.current ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(scrollOffsets, initialScrollOffsets.current) : defaultCoordinates;\n}\n\nfunction useSensorSetup(sensors) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.canUseDOM) {\n      return;\n    }\n\n    const teardownFns = sensors.map(_ref => {\n      let {\n        sensor\n      } = _ref;\n      return sensor.setup == null ? void 0 : sensor.setup();\n    });\n    return () => {\n      for (const teardown of teardownFns) {\n        teardown == null ? void 0 : teardown();\n      }\n    };\n  }, // TO-DO: Sensors length could theoretically change which would not be a valid dependency\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  sensors.map(_ref2 => {\n    let {\n      sensor\n    } = _ref2;\n    return sensor;\n  }));\n}\n\nfunction useSyntheticListeners(listeners, id) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    return listeners.reduce((acc, _ref) => {\n      let {\n        eventName,\n        handler\n      } = _ref;\n\n      acc[eventName] = event => {\n        handler(event, id);\n      };\n\n      return acc;\n    }, {});\n  }, [listeners, id]);\n}\n\nfunction useWindowRect(element) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => element ? getWindowClientRect(element) : null, [element]);\n}\n\nconst defaultValue$2 = [];\nfunction useRects(elements, measure) {\n  if (measure === void 0) {\n    measure = getClientRect;\n  }\n\n  const [firstElement] = elements;\n  const windowRect = useWindowRect(firstElement ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(firstElement) : null);\n  const [rects, setRects] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(defaultValue$2);\n\n  function measureRects() {\n    setRects(() => {\n      if (!elements.length) {\n        return defaultValue$2;\n      }\n\n      return elements.map(element => isDocumentScrollingElement(element) ? windowRect : new Rect(measure(element), element));\n    });\n  }\n\n  const resizeObserver = useResizeObserver({\n    callback: measureRects\n  });\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    resizeObserver == null ? void 0 : resizeObserver.disconnect();\n    measureRects();\n    elements.forEach(element => resizeObserver == null ? void 0 : resizeObserver.observe(element));\n  }, [elements]);\n  return rects;\n}\n\nfunction getMeasurableNode(node) {\n  if (!node) {\n    return null;\n  }\n\n  if (node.children.length > 1) {\n    return node;\n  }\n\n  const firstChild = node.children[0];\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(firstChild) ? firstChild : node;\n}\n\nfunction useDragOverlayMeasuring(_ref) {\n  let {\n    measure\n  } = _ref;\n  const [rect, setRect] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const handleResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(entries => {\n    for (const {\n      target\n    } of entries) {\n      if ((0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isHTMLElement)(target)) {\n        setRect(rect => {\n          const newRect = measure(target);\n          return rect ? { ...rect,\n            width: newRect.width,\n            height: newRect.height\n          } : newRect;\n        });\n        break;\n      }\n    }\n  }, [measure]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize\n  });\n  const handleNodeChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(element => {\n    const node = getMeasurableNode(element);\n    resizeObserver == null ? void 0 : resizeObserver.disconnect();\n\n    if (node) {\n      resizeObserver == null ? void 0 : resizeObserver.observe(node);\n    }\n\n    setRect(node ? measure(node) : null);\n  }, [measure, resizeObserver]);\n  const [nodeRef, setRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)(handleNodeChange);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    nodeRef,\n    rect,\n    setRef\n  }), [rect, nodeRef, setRef]);\n}\n\nconst defaultSensors = [{\n  sensor: PointerSensor,\n  options: {}\n}, {\n  sensor: KeyboardSensor,\n  options: {}\n}];\nconst defaultData = {\n  current: {}\n};\nconst defaultMeasuringConfiguration = {\n  draggable: {\n    measure: getTransformAgnosticClientRect\n  },\n  droppable: {\n    measure: getTransformAgnosticClientRect,\n    strategy: MeasuringStrategy.WhileDragging,\n    frequency: MeasuringFrequency.Optimized\n  },\n  dragOverlay: {\n    measure: getClientRect\n  }\n};\n\nclass DroppableContainersMap extends Map {\n  get(id) {\n    var _super$get;\n\n    return id != null ? (_super$get = super.get(id)) != null ? _super$get : undefined : undefined;\n  }\n\n  toArray() {\n    return Array.from(this.values());\n  }\n\n  getEnabled() {\n    return this.toArray().filter(_ref => {\n      let {\n        disabled\n      } = _ref;\n      return !disabled;\n    });\n  }\n\n  getNodeFor(id) {\n    var _this$get$node$curren, _this$get;\n\n    return (_this$get$node$curren = (_this$get = this.get(id)) == null ? void 0 : _this$get.node.current) != null ? _this$get$node$curren : undefined;\n  }\n\n}\n\nconst defaultPublicContext = {\n  activatorEvent: null,\n  active: null,\n  activeNode: null,\n  activeNodeRect: null,\n  collisions: null,\n  containerNodeRect: null,\n  draggableNodes: /*#__PURE__*/new Map(),\n  droppableRects: /*#__PURE__*/new Map(),\n  droppableContainers: /*#__PURE__*/new DroppableContainersMap(),\n  over: null,\n  dragOverlay: {\n    nodeRef: {\n      current: null\n    },\n    rect: null,\n    setRef: noop\n  },\n  scrollableAncestors: [],\n  scrollableAncestorRects: [],\n  measuringConfiguration: defaultMeasuringConfiguration,\n  measureDroppableContainers: noop,\n  windowRect: null,\n  measuringScheduled: false\n};\nconst defaultInternalContext = {\n  activatorEvent: null,\n  activators: [],\n  active: null,\n  activeNodeRect: null,\n  ariaDescribedById: {\n    draggable: ''\n  },\n  dispatch: noop,\n  draggableNodes: /*#__PURE__*/new Map(),\n  over: null,\n  measureDroppableContainers: noop\n};\nconst InternalContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(defaultInternalContext);\nconst PublicContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(defaultPublicContext);\n\nfunction getInitialState() {\n  return {\n    draggable: {\n      active: null,\n      initialCoordinates: {\n        x: 0,\n        y: 0\n      },\n      nodes: new Map(),\n      translate: {\n        x: 0,\n        y: 0\n      }\n    },\n    droppable: {\n      containers: new DroppableContainersMap()\n    }\n  };\n}\nfunction reducer(state, action) {\n  switch (action.type) {\n    case Action.DragStart:\n      return { ...state,\n        draggable: { ...state.draggable,\n          initialCoordinates: action.initialCoordinates,\n          active: action.active\n        }\n      };\n\n    case Action.DragMove:\n      if (state.draggable.active == null) {\n        return state;\n      }\n\n      return { ...state,\n        draggable: { ...state.draggable,\n          translate: {\n            x: action.coordinates.x - state.draggable.initialCoordinates.x,\n            y: action.coordinates.y - state.draggable.initialCoordinates.y\n          }\n        }\n      };\n\n    case Action.DragEnd:\n    case Action.DragCancel:\n      return { ...state,\n        draggable: { ...state.draggable,\n          active: null,\n          initialCoordinates: {\n            x: 0,\n            y: 0\n          },\n          translate: {\n            x: 0,\n            y: 0\n          }\n        }\n      };\n\n    case Action.RegisterDroppable:\n      {\n        const {\n          element\n        } = action;\n        const {\n          id\n        } = element;\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, element);\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    case Action.SetDroppableDisabled:\n      {\n        const {\n          id,\n          key,\n          disabled\n        } = action;\n        const element = state.droppable.containers.get(id);\n\n        if (!element || key !== element.key) {\n          return state;\n        }\n\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.set(id, { ...element,\n          disabled\n        });\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    case Action.UnregisterDroppable:\n      {\n        const {\n          id,\n          key\n        } = action;\n        const element = state.droppable.containers.get(id);\n\n        if (!element || key !== element.key) {\n          return state;\n        }\n\n        const containers = new DroppableContainersMap(state.droppable.containers);\n        containers.delete(id);\n        return { ...state,\n          droppable: { ...state.droppable,\n            containers\n          }\n        };\n      }\n\n    default:\n      {\n        return state;\n      }\n  }\n}\n\nfunction RestoreFocus(_ref) {\n  let {\n    disabled\n  } = _ref;\n  const {\n    active,\n    activatorEvent,\n    draggableNodes\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(InternalContext);\n  const previousActivatorEvent = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(activatorEvent);\n  const previousActiveId = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(active == null ? void 0 : active.id); // Restore keyboard focus on the activator node\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) {\n      return;\n    }\n\n    if (!activatorEvent && previousActivatorEvent && previousActiveId != null) {\n      if (!(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(previousActivatorEvent)) {\n        return;\n      }\n\n      if (document.activeElement === previousActivatorEvent.target) {\n        // No need to restore focus\n        return;\n      }\n\n      const draggableNode = draggableNodes.get(previousActiveId);\n\n      if (!draggableNode) {\n        return;\n      }\n\n      const {\n        activatorNode,\n        node\n      } = draggableNode;\n\n      if (!activatorNode.current && !node.current) {\n        return;\n      }\n\n      requestAnimationFrame(() => {\n        for (const element of [activatorNode.current, node.current]) {\n          if (!element) {\n            continue;\n          }\n\n          const focusableNode = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.findFirstFocusableNode)(element);\n\n          if (focusableNode) {\n            focusableNode.focus();\n            break;\n          }\n        }\n      });\n    }\n  }, [activatorEvent, disabled, draggableNodes, previousActiveId, previousActivatorEvent]);\n  return null;\n}\n\nfunction applyModifiers(modifiers, _ref) {\n  let {\n    transform,\n    ...args\n  } = _ref;\n  return modifiers != null && modifiers.length ? modifiers.reduce((accumulator, modifier) => {\n    return modifier({\n      transform: accumulator,\n      ...args\n    });\n  }, transform) : transform;\n}\n\nfunction useMeasuringConfiguration(config) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    draggable: { ...defaultMeasuringConfiguration.draggable,\n      ...(config == null ? void 0 : config.draggable)\n    },\n    droppable: { ...defaultMeasuringConfiguration.droppable,\n      ...(config == null ? void 0 : config.droppable)\n    },\n    dragOverlay: { ...defaultMeasuringConfiguration.dragOverlay,\n      ...(config == null ? void 0 : config.dragOverlay)\n    }\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [config == null ? void 0 : config.draggable, config == null ? void 0 : config.droppable, config == null ? void 0 : config.dragOverlay]);\n}\n\nfunction useLayoutShiftScrollCompensation(_ref) {\n  let {\n    activeNode,\n    measure,\n    initialRect,\n    config = true\n  } = _ref;\n  const initialized = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const {\n    x,\n    y\n  } = typeof config === 'boolean' ? {\n    x: config,\n    y: config\n  } : config;\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    const disabled = !x && !y;\n\n    if (disabled || !activeNode) {\n      initialized.current = false;\n      return;\n    }\n\n    if (initialized.current || !initialRect) {\n      // Return early if layout shift scroll compensation was already attempted\n      // or if there is no initialRect to compare to.\n      return;\n    } // Get the most up to date node ref for the active draggable\n\n\n    const node = activeNode == null ? void 0 : activeNode.node.current;\n\n    if (!node || node.isConnected === false) {\n      // Return early if there is no attached node ref or if the node is\n      // disconnected from the document.\n      return;\n    }\n\n    const rect = measure(node);\n    const rectDelta = getRectDelta(rect, initialRect);\n\n    if (!x) {\n      rectDelta.x = 0;\n    }\n\n    if (!y) {\n      rectDelta.y = 0;\n    } // Only perform layout shift scroll compensation once\n\n\n    initialized.current = true;\n\n    if (Math.abs(rectDelta.x) > 0 || Math.abs(rectDelta.y) > 0) {\n      const firstScrollableAncestor = getFirstScrollableAncestor(node);\n\n      if (firstScrollableAncestor) {\n        firstScrollableAncestor.scrollBy({\n          top: rectDelta.y,\n          left: rectDelta.x\n        });\n      }\n    }\n  }, [activeNode, x, y, initialRect, measure]);\n}\n\nconst ActiveDraggableContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({ ...defaultCoordinates,\n  scaleX: 1,\n  scaleY: 1\n});\nvar Status;\n\n(function (Status) {\n  Status[Status[\"Uninitialized\"] = 0] = \"Uninitialized\";\n  Status[Status[\"Initializing\"] = 1] = \"Initializing\";\n  Status[Status[\"Initialized\"] = 2] = \"Initialized\";\n})(Status || (Status = {}));\n\nconst DndContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(function DndContext(_ref) {\n  var _sensorContext$curren, _dragOverlay$nodeRef$, _dragOverlay$rect, _over$rect;\n\n  let {\n    id,\n    accessibility,\n    autoScroll = true,\n    children,\n    sensors = defaultSensors,\n    collisionDetection = rectIntersection,\n    measuring,\n    modifiers,\n    ...props\n  } = _ref;\n  const store = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(reducer, undefined, getInitialState);\n  const [state, dispatch] = store;\n  const [dispatchMonitorEvent, registerMonitorListener] = useDndMonitorProvider();\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(Status.Uninitialized);\n  const isInitialized = status === Status.Initialized;\n  const {\n    draggable: {\n      active: activeId,\n      nodes: draggableNodes,\n      translate\n    },\n    droppable: {\n      containers: droppableContainers\n    }\n  } = state;\n  const node = activeId != null ? draggableNodes.get(activeId) : null;\n  const activeRects = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    initial: null,\n    translated: null\n  });\n  const active = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    var _node$data;\n\n    return activeId != null ? {\n      id: activeId,\n      // It's possible for the active node to unmount while dragging\n      data: (_node$data = node == null ? void 0 : node.data) != null ? _node$data : defaultData,\n      rect: activeRects\n    } : null;\n  }, [activeId, node]);\n  const activeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [activeSensor, setActiveSensor] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [activatorEvent, setActivatorEvent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const latestProps = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(props, Object.values(props));\n  const draggableDescribedById = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(\"DndDescribedBy\", id);\n  const enabledDroppableContainers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => droppableContainers.getEnabled(), [droppableContainers]);\n  const measuringConfiguration = useMeasuringConfiguration(measuring);\n  const {\n    droppableRects,\n    measureDroppableContainers,\n    measuringScheduled\n  } = useDroppableMeasuring(enabledDroppableContainers, {\n    dragging: isInitialized,\n    dependencies: [translate.x, translate.y],\n    config: measuringConfiguration.droppable\n  });\n  const activeNode = useCachedNode(draggableNodes, activeId);\n  const activationCoordinates = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => activatorEvent ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getEventCoordinates)(activatorEvent) : null, [activatorEvent]);\n  const autoScrollOptions = getAutoScrollerOptions();\n  const initialActiveNodeRect = useInitialRect(activeNode, measuringConfiguration.draggable.measure);\n  useLayoutShiftScrollCompensation({\n    activeNode: activeId != null ? draggableNodes.get(activeId) : null,\n    config: autoScrollOptions.layoutShiftCompensation,\n    initialRect: initialActiveNodeRect,\n    measure: measuringConfiguration.draggable.measure\n  });\n  const activeNodeRect = useRect(activeNode, measuringConfiguration.draggable.measure, initialActiveNodeRect);\n  const containerNodeRect = useRect(activeNode ? activeNode.parentElement : null);\n  const sensorContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    activatorEvent: null,\n    active: null,\n    activeNode,\n    collisionRect: null,\n    collisions: null,\n    droppableRects,\n    draggableNodes,\n    draggingNode: null,\n    draggingNodeRect: null,\n    droppableContainers,\n    over: null,\n    scrollableAncestors: [],\n    scrollAdjustedTranslate: null\n  });\n  const overNode = droppableContainers.getNodeFor((_sensorContext$curren = sensorContext.current.over) == null ? void 0 : _sensorContext$curren.id);\n  const dragOverlay = useDragOverlayMeasuring({\n    measure: measuringConfiguration.dragOverlay.measure\n  }); // Use the rect of the drag overlay if it is mounted\n\n  const draggingNode = (_dragOverlay$nodeRef$ = dragOverlay.nodeRef.current) != null ? _dragOverlay$nodeRef$ : activeNode;\n  const draggingNodeRect = isInitialized ? (_dragOverlay$rect = dragOverlay.rect) != null ? _dragOverlay$rect : activeNodeRect : null;\n  const usesDragOverlay = Boolean(dragOverlay.nodeRef.current && dragOverlay.rect); // The delta between the previous and new position of the draggable node\n  // is only relevant when there is no drag overlay\n\n  const nodeRectDelta = useRectDelta(usesDragOverlay ? null : activeNodeRect); // Get the window rect of the dragging node\n\n  const windowRect = useWindowRect(draggingNode ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(draggingNode) : null); // Get scrollable ancestors of the dragging node\n\n  const scrollableAncestors = useScrollableAncestors(isInitialized ? overNode != null ? overNode : activeNode : null);\n  const scrollableAncestorRects = useRects(scrollableAncestors); // Apply modifiers\n\n  const modifiedTranslate = applyModifiers(modifiers, {\n    transform: {\n      x: translate.x - nodeRectDelta.x,\n      y: translate.y - nodeRectDelta.y,\n      scaleX: 1,\n      scaleY: 1\n    },\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect,\n    over: sensorContext.current.over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  });\n  const pointerCoordinates = activationCoordinates ? (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(activationCoordinates, translate) : null;\n  const scrollOffsets = useScrollOffsets(scrollableAncestors); // Represents the scroll delta since dragging was initiated\n\n  const scrollAdjustment = useScrollOffsetsDelta(scrollOffsets); // Represents the scroll delta since the last time the active node rect was measured\n\n  const activeNodeScrollDelta = useScrollOffsetsDelta(scrollOffsets, [activeNodeRect]);\n  const scrollAdjustedTranslate = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(modifiedTranslate, scrollAdjustment);\n  const collisionRect = draggingNodeRect ? getAdjustedRect(draggingNodeRect, modifiedTranslate) : null;\n  const collisions = active && collisionRect ? collisionDetection({\n    active,\n    collisionRect,\n    droppableRects,\n    droppableContainers: enabledDroppableContainers,\n    pointerCoordinates\n  }) : null;\n  const overId = getFirstCollision(collisions, 'id');\n  const [over, setOver] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null); // When there is no drag overlay used, we need to account for the\n  // window scroll delta\n\n  const appliedTranslate = usesDragOverlay ? modifiedTranslate : (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.add)(modifiedTranslate, activeNodeScrollDelta);\n  const transform = adjustScale(appliedTranslate, (_over$rect = over == null ? void 0 : over.rect) != null ? _over$rect : null, activeNodeRect);\n  const activeSensorRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const instantiateSensor = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((event, _ref2) => {\n    let {\n      sensor: Sensor,\n      options\n    } = _ref2;\n\n    if (activeRef.current == null) {\n      return;\n    }\n\n    const activeNode = draggableNodes.get(activeRef.current);\n\n    if (!activeNode) {\n      return;\n    }\n\n    const activatorEvent = event.nativeEvent;\n    const sensorInstance = new Sensor({\n      active: activeRef.current,\n      activeNode,\n      event: activatorEvent,\n      options,\n      // Sensors need to be instantiated with refs for arguments that change over time\n      // otherwise they are frozen in time with the stale arguments\n      context: sensorContext,\n\n      onAbort(id) {\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragAbort\n        } = latestProps.current;\n        const event = {\n          id\n        };\n        onDragAbort == null ? void 0 : onDragAbort(event);\n        dispatchMonitorEvent({\n          type: 'onDragAbort',\n          event\n        });\n      },\n\n      onPending(id, constraint, initialCoordinates, offset) {\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragPending\n        } = latestProps.current;\n        const event = {\n          id,\n          constraint,\n          initialCoordinates,\n          offset\n        };\n        onDragPending == null ? void 0 : onDragPending(event);\n        dispatchMonitorEvent({\n          type: 'onDragPending',\n          event\n        });\n      },\n\n      onStart(initialCoordinates) {\n        const id = activeRef.current;\n\n        if (id == null) {\n          return;\n        }\n\n        const draggableNode = draggableNodes.get(id);\n\n        if (!draggableNode) {\n          return;\n        }\n\n        const {\n          onDragStart\n        } = latestProps.current;\n        const event = {\n          activatorEvent,\n          active: {\n            id,\n            data: draggableNode.data,\n            rect: activeRects\n          }\n        };\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n          onDragStart == null ? void 0 : onDragStart(event);\n          setStatus(Status.Initializing);\n          dispatch({\n            type: Action.DragStart,\n            initialCoordinates,\n            active: id\n          });\n          dispatchMonitorEvent({\n            type: 'onDragStart',\n            event\n          });\n          setActiveSensor(activeSensorRef.current);\n          setActivatorEvent(activatorEvent);\n        });\n      },\n\n      onMove(coordinates) {\n        dispatch({\n          type: Action.DragMove,\n          coordinates\n        });\n      },\n\n      onEnd: createHandler(Action.DragEnd),\n      onCancel: createHandler(Action.DragCancel)\n    });\n    activeSensorRef.current = sensorInstance;\n\n    function createHandler(type) {\n      return async function handler() {\n        const {\n          active,\n          collisions,\n          over,\n          scrollAdjustedTranslate\n        } = sensorContext.current;\n        let event = null;\n\n        if (active && scrollAdjustedTranslate) {\n          const {\n            cancelDrop\n          } = latestProps.current;\n          event = {\n            activatorEvent,\n            active: active,\n            collisions,\n            delta: scrollAdjustedTranslate,\n            over\n          };\n\n          if (type === Action.DragEnd && typeof cancelDrop === 'function') {\n            const shouldCancel = await Promise.resolve(cancelDrop(event));\n\n            if (shouldCancel) {\n              type = Action.DragCancel;\n            }\n          }\n        }\n\n        activeRef.current = null;\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n          dispatch({\n            type\n          });\n          setStatus(Status.Uninitialized);\n          setOver(null);\n          setActiveSensor(null);\n          setActivatorEvent(null);\n          activeSensorRef.current = null;\n          const eventName = type === Action.DragEnd ? 'onDragEnd' : 'onDragCancel';\n\n          if (event) {\n            const handler = latestProps.current[eventName];\n            handler == null ? void 0 : handler(event);\n            dispatchMonitorEvent({\n              type: eventName,\n              event\n            });\n          }\n        });\n      };\n    }\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes]);\n  const bindActivatorToSensorInstantiator = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((handler, sensor) => {\n    return (event, active) => {\n      const nativeEvent = event.nativeEvent;\n      const activeDraggableNode = draggableNodes.get(active);\n\n      if ( // Another sensor is already instantiating\n      activeRef.current !== null || // No active draggable\n      !activeDraggableNode || // Event has already been captured\n      nativeEvent.dndKit || nativeEvent.defaultPrevented) {\n        return;\n      }\n\n      const activationContext = {\n        active: activeDraggableNode\n      };\n      const shouldActivate = handler(event, sensor.options, activationContext);\n\n      if (shouldActivate === true) {\n        nativeEvent.dndKit = {\n          capturedBy: sensor.sensor\n        };\n        activeRef.current = active;\n        instantiateSensor(event, sensor);\n      }\n    };\n  }, [draggableNodes, instantiateSensor]);\n  const activators = useCombineActivators(sensors, bindActivatorToSensorInstantiator);\n  useSensorSetup(sensors);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (activeNodeRect && status === Status.Initializing) {\n      setStatus(Status.Initialized);\n    }\n  }, [activeNodeRect, status]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      onDragMove\n    } = latestProps.current;\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      over\n    } = sensorContext.current;\n\n    if (!active || !activatorEvent) {\n      return;\n    }\n\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n      onDragMove == null ? void 0 : onDragMove(event);\n      dispatchMonitorEvent({\n        type: 'onDragMove',\n        event\n      });\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [scrollAdjustedTranslate.x, scrollAdjustedTranslate.y]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      active,\n      activatorEvent,\n      collisions,\n      droppableContainers,\n      scrollAdjustedTranslate\n    } = sensorContext.current;\n\n    if (!active || activeRef.current == null || !activatorEvent || !scrollAdjustedTranslate) {\n      return;\n    }\n\n    const {\n      onDragOver\n    } = latestProps.current;\n    const overContainer = droppableContainers.get(overId);\n    const over = overContainer && overContainer.rect.current ? {\n      id: overContainer.id,\n      rect: overContainer.rect.current,\n      data: overContainer.data,\n      disabled: overContainer.disabled\n    } : null;\n    const event = {\n      active,\n      activatorEvent,\n      collisions,\n      delta: {\n        x: scrollAdjustedTranslate.x,\n        y: scrollAdjustedTranslate.y\n      },\n      over\n    };\n    (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.unstable_batchedUpdates)(() => {\n      setOver(over);\n      onDragOver == null ? void 0 : onDragOver(event);\n      dispatchMonitorEvent({\n        type: 'onDragOver',\n        event\n      });\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [overId]);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    sensorContext.current = {\n      activatorEvent,\n      active,\n      activeNode,\n      collisionRect,\n      collisions,\n      droppableRects,\n      draggableNodes,\n      draggingNode,\n      draggingNodeRect,\n      droppableContainers,\n      over,\n      scrollableAncestors,\n      scrollAdjustedTranslate\n    };\n    activeRects.current = {\n      initial: draggingNodeRect,\n      translated: collisionRect\n    };\n  }, [active, activeNode, collisions, collisionRect, draggableNodes, draggingNode, draggingNodeRect, droppableRects, droppableContainers, over, scrollableAncestors, scrollAdjustedTranslate]);\n  useAutoScroller({ ...autoScrollOptions,\n    delta: translate,\n    draggingRect: collisionRect,\n    pointerCoordinates,\n    scrollableAncestors,\n    scrollableAncestorRects\n  });\n  const publicContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const context = {\n      active,\n      activeNode,\n      activeNodeRect,\n      activatorEvent,\n      collisions,\n      containerNodeRect,\n      dragOverlay,\n      draggableNodes,\n      droppableContainers,\n      droppableRects,\n      over,\n      measureDroppableContainers,\n      scrollableAncestors,\n      scrollableAncestorRects,\n      measuringConfiguration,\n      measuringScheduled,\n      windowRect\n    };\n    return context;\n  }, [active, activeNode, activeNodeRect, activatorEvent, collisions, containerNodeRect, dragOverlay, draggableNodes, droppableContainers, droppableRects, over, measureDroppableContainers, scrollableAncestors, scrollableAncestorRects, measuringConfiguration, measuringScheduled, windowRect]);\n  const internalContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const context = {\n      activatorEvent,\n      activators,\n      active,\n      activeNodeRect,\n      ariaDescribedById: {\n        draggable: draggableDescribedById\n      },\n      dispatch,\n      draggableNodes,\n      over,\n      measureDroppableContainers\n    };\n    return context;\n  }, [activatorEvent, activators, active, activeNodeRect, dispatch, draggableDescribedById, draggableNodes, over, measureDroppableContainers]);\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(DndMonitorContext.Provider, {\n    value: registerMonitorListener\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(InternalContext.Provider, {\n    value: internalContext\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(PublicContext.Provider, {\n    value: publicContext\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ActiveDraggableContext.Provider, {\n    value: transform\n  }, children)), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(RestoreFocus, {\n    disabled: (accessibility == null ? void 0 : accessibility.restoreFocus) === false\n  })), react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Accessibility, { ...accessibility,\n    hiddenTextDescribedById: draggableDescribedById\n  }));\n\n  function getAutoScrollerOptions() {\n    const activeSensorDisablesAutoscroll = (activeSensor == null ? void 0 : activeSensor.autoScrollEnabled) === false;\n    const autoScrollGloballyDisabled = typeof autoScroll === 'object' ? autoScroll.enabled === false : autoScroll === false;\n    const enabled = isInitialized && !activeSensorDisablesAutoscroll && !autoScrollGloballyDisabled;\n\n    if (typeof autoScroll === 'object') {\n      return { ...autoScroll,\n        enabled\n      };\n    }\n\n    return {\n      enabled\n    };\n  }\n});\n\nconst NullContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nconst defaultRole = 'button';\nconst ID_PREFIX = 'Draggable';\nfunction useDraggable(_ref) {\n  let {\n    id,\n    data,\n    disabled = false,\n    attributes\n  } = _ref;\n  const key = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(ID_PREFIX);\n  const {\n    activators,\n    activatorEvent,\n    active,\n    activeNodeRect,\n    ariaDescribedById,\n    draggableNodes,\n    over\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(InternalContext);\n  const {\n    role = defaultRole,\n    roleDescription = 'draggable',\n    tabIndex = 0\n  } = attributes != null ? attributes : {};\n  const isDragging = (active == null ? void 0 : active.id) === id;\n  const transform = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(isDragging ? ActiveDraggableContext : NullContext);\n  const [node, setNodeRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)();\n  const [activatorNode, setActivatorNodeRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)();\n  const listeners = useSyntheticListeners(activators, id);\n  const dataRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(data);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    draggableNodes.set(id, {\n      id,\n      key,\n      node,\n      activatorNode,\n      data: dataRef\n    });\n    return () => {\n      const node = draggableNodes.get(id);\n\n      if (node && node.key === key) {\n        draggableNodes.delete(id);\n      }\n    };\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [draggableNodes, id]);\n  const memoizedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    role,\n    tabIndex,\n    'aria-disabled': disabled,\n    'aria-pressed': isDragging && role === defaultRole ? true : undefined,\n    'aria-roledescription': roleDescription,\n    'aria-describedby': ariaDescribedById.draggable\n  }), [disabled, role, tabIndex, isDragging, roleDescription, ariaDescribedById.draggable]);\n  return {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes: memoizedAttributes,\n    isDragging,\n    listeners: disabled ? undefined : listeners,\n    node,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    transform\n  };\n}\n\nfunction useDndContext() {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(PublicContext);\n}\n\nconst ID_PREFIX$1 = 'Droppable';\nconst defaultResizeObserverConfig = {\n  timeout: 25\n};\nfunction useDroppable(_ref) {\n  let {\n    data,\n    disabled = false,\n    id,\n    resizeObserverConfig\n  } = _ref;\n  const key = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(ID_PREFIX$1);\n  const {\n    active,\n    dispatch,\n    over,\n    measureDroppableContainers\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(InternalContext);\n  const previous = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    disabled\n  });\n  const resizeObserverConnected = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const rect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const callbackId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const {\n    disabled: resizeObserverDisabled,\n    updateMeasurementsFor,\n    timeout: resizeObserverTimeout\n  } = { ...defaultResizeObserverConfig,\n    ...resizeObserverConfig\n  };\n  const ids = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(updateMeasurementsFor != null ? updateMeasurementsFor : id);\n  const handleResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!resizeObserverConnected.current) {\n      // ResizeObserver invokes the `handleResize` callback as soon as `observe` is called,\n      // assuming the element is rendered and displayed.\n      resizeObserverConnected.current = true;\n      return;\n    }\n\n    if (callbackId.current != null) {\n      clearTimeout(callbackId.current);\n    }\n\n    callbackId.current = setTimeout(() => {\n      measureDroppableContainers(Array.isArray(ids.current) ? ids.current : [ids.current]);\n      callbackId.current = null;\n    }, resizeObserverTimeout);\n  }, //eslint-disable-next-line react-hooks/exhaustive-deps\n  [resizeObserverTimeout]);\n  const resizeObserver = useResizeObserver({\n    callback: handleResize,\n    disabled: resizeObserverDisabled || !active\n  });\n  const handleNodeChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((newElement, previousElement) => {\n    if (!resizeObserver) {\n      return;\n    }\n\n    if (previousElement) {\n      resizeObserver.unobserve(previousElement);\n      resizeObserverConnected.current = false;\n    }\n\n    if (newElement) {\n      resizeObserver.observe(newElement);\n    }\n  }, [resizeObserver]);\n  const [nodeRef, setNodeRef] = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useNodeRef)(handleNodeChange);\n  const dataRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useLatestValue)(data);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!resizeObserver || !nodeRef.current) {\n      return;\n    }\n\n    resizeObserver.disconnect();\n    resizeObserverConnected.current = false;\n    resizeObserver.observe(nodeRef.current);\n  }, [nodeRef, resizeObserver]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    dispatch({\n      type: Action.RegisterDroppable,\n      element: {\n        id,\n        key,\n        disabled,\n        node: nodeRef,\n        rect,\n        data: dataRef\n      }\n    });\n    return () => dispatch({\n      type: Action.UnregisterDroppable,\n      key,\n      id\n    });\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [id]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled !== previous.current.disabled) {\n      dispatch({\n        type: Action.SetDroppableDisabled,\n        id,\n        key,\n        disabled\n      });\n      previous.current.disabled = disabled;\n    }\n  }, [id, key, disabled, dispatch]);\n  return {\n    active,\n    rect,\n    isOver: (over == null ? void 0 : over.id) === id,\n    node: nodeRef,\n    over,\n    setNodeRef\n  };\n}\n\nfunction AnimationManager(_ref) {\n  let {\n    animation,\n    children\n  } = _ref;\n  const [clonedChildren, setClonedChildren] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [element, setElement] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const previousChildren = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.usePrevious)(children);\n\n  if (!children && !clonedChildren && previousChildren) {\n    setClonedChildren(previousChildren);\n  }\n\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (!element) {\n      return;\n    }\n\n    const key = clonedChildren == null ? void 0 : clonedChildren.key;\n    const id = clonedChildren == null ? void 0 : clonedChildren.props.id;\n\n    if (key == null || id == null) {\n      setClonedChildren(null);\n      return;\n    }\n\n    Promise.resolve(animation(id, element)).then(() => {\n      setClonedChildren(null);\n    });\n  }, [animation, clonedChildren, element]);\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, children, clonedChildren ? (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(clonedChildren, {\n    ref: setElement\n  }) : null);\n}\n\nconst defaultTransform = {\n  x: 0,\n  y: 0,\n  scaleX: 1,\n  scaleY: 1\n};\nfunction NullifiedContextProvider(_ref) {\n  let {\n    children\n  } = _ref;\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(InternalContext.Provider, {\n    value: defaultInternalContext\n  }, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(ActiveDraggableContext.Provider, {\n    value: defaultTransform\n  }, children));\n}\n\nconst baseStyles = {\n  position: 'fixed',\n  touchAction: 'none'\n};\n\nconst defaultTransition = activatorEvent => {\n  const isKeyboardActivator = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(activatorEvent);\n  return isKeyboardActivator ? 'transform 250ms ease' : undefined;\n};\n\nconst PositionedOverlay = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((_ref, ref) => {\n  let {\n    as,\n    activatorEvent,\n    adjustScale,\n    children,\n    className,\n    rect,\n    style,\n    transform,\n    transition = defaultTransition\n  } = _ref;\n\n  if (!rect) {\n    return null;\n  }\n\n  const scaleAdjustedTransform = adjustScale ? transform : { ...transform,\n    scaleX: 1,\n    scaleY: 1\n  };\n  const styles = { ...baseStyles,\n    width: rect.width,\n    height: rect.height,\n    top: rect.top,\n    left: rect.left,\n    transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transform.toString(scaleAdjustedTransform),\n    transformOrigin: adjustScale && activatorEvent ? getRelativeTransformOrigin(activatorEvent, rect) : undefined,\n    transition: typeof transition === 'function' ? transition(activatorEvent) : transition,\n    ...style\n  };\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(as, {\n    className,\n    style: styles,\n    ref\n  }, children);\n});\n\nconst defaultDropAnimationSideEffects = options => _ref => {\n  let {\n    active,\n    dragOverlay\n  } = _ref;\n  const originalStyles = {};\n  const {\n    styles,\n    className\n  } = options;\n\n  if (styles != null && styles.active) {\n    for (const [key, value] of Object.entries(styles.active)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      originalStyles[key] = active.node.style.getPropertyValue(key);\n      active.node.style.setProperty(key, value);\n    }\n  }\n\n  if (styles != null && styles.dragOverlay) {\n    for (const [key, value] of Object.entries(styles.dragOverlay)) {\n      if (value === undefined) {\n        continue;\n      }\n\n      dragOverlay.node.style.setProperty(key, value);\n    }\n  }\n\n  if (className != null && className.active) {\n    active.node.classList.add(className.active);\n  }\n\n  if (className != null && className.dragOverlay) {\n    dragOverlay.node.classList.add(className.dragOverlay);\n  }\n\n  return function cleanup() {\n    for (const [key, value] of Object.entries(originalStyles)) {\n      active.node.style.setProperty(key, value);\n    }\n\n    if (className != null && className.active) {\n      active.node.classList.remove(className.active);\n    }\n  };\n};\n\nconst defaultKeyframeResolver = _ref2 => {\n  let {\n    transform: {\n      initial,\n      final\n    }\n  } = _ref2;\n  return [{\n    transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transform.toString(initial)\n  }, {\n    transform: _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transform.toString(final)\n  }];\n};\n\nconst defaultDropAnimationConfiguration = {\n  duration: 250,\n  easing: 'ease',\n  keyframes: defaultKeyframeResolver,\n  sideEffects: /*#__PURE__*/defaultDropAnimationSideEffects({\n    styles: {\n      active: {\n        opacity: '0'\n      }\n    }\n  })\n};\nfunction useDropAnimation(_ref3) {\n  let {\n    config,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  } = _ref3;\n  return (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useEvent)((id, node) => {\n    if (config === null) {\n      return;\n    }\n\n    const activeDraggable = draggableNodes.get(id);\n\n    if (!activeDraggable) {\n      return;\n    }\n\n    const activeNode = activeDraggable.node.current;\n\n    if (!activeNode) {\n      return;\n    }\n\n    const measurableNode = getMeasurableNode(node);\n\n    if (!measurableNode) {\n      return;\n    }\n\n    const {\n      transform\n    } = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.getWindow)(node).getComputedStyle(node);\n    const parsedTransform = parseTransform(transform);\n\n    if (!parsedTransform) {\n      return;\n    }\n\n    const animation = typeof config === 'function' ? config : createDefaultDropAnimation(config);\n    scrollIntoViewIfNeeded(activeNode, measuringConfiguration.draggable.measure);\n    return animation({\n      active: {\n        id,\n        data: activeDraggable.data,\n        node: activeNode,\n        rect: measuringConfiguration.draggable.measure(activeNode)\n      },\n      draggableNodes,\n      dragOverlay: {\n        node,\n        rect: measuringConfiguration.dragOverlay.measure(measurableNode)\n      },\n      droppableContainers,\n      measuringConfiguration,\n      transform: parsedTransform\n    });\n  });\n}\n\nfunction createDefaultDropAnimation(options) {\n  const {\n    duration,\n    easing,\n    sideEffects,\n    keyframes\n  } = { ...defaultDropAnimationConfiguration,\n    ...options\n  };\n  return _ref4 => {\n    let {\n      active,\n      dragOverlay,\n      transform,\n      ...rest\n    } = _ref4;\n\n    if (!duration) {\n      // Do not animate if animation duration is zero.\n      return;\n    }\n\n    const delta = {\n      x: dragOverlay.rect.left - active.rect.left,\n      y: dragOverlay.rect.top - active.rect.top\n    };\n    const scale = {\n      scaleX: transform.scaleX !== 1 ? active.rect.width * transform.scaleX / dragOverlay.rect.width : 1,\n      scaleY: transform.scaleY !== 1 ? active.rect.height * transform.scaleY / dragOverlay.rect.height : 1\n    };\n    const finalTransform = {\n      x: transform.x - delta.x,\n      y: transform.y - delta.y,\n      ...scale\n    };\n    const animationKeyframes = keyframes({ ...rest,\n      active,\n      dragOverlay,\n      transform: {\n        initial: transform,\n        final: finalTransform\n      }\n    });\n    const [firstKeyframe] = animationKeyframes;\n    const lastKeyframe = animationKeyframes[animationKeyframes.length - 1];\n\n    if (JSON.stringify(firstKeyframe) === JSON.stringify(lastKeyframe)) {\n      // The start and end keyframes are the same, infer that there is no animation needed.\n      return;\n    }\n\n    const cleanup = sideEffects == null ? void 0 : sideEffects({\n      active,\n      dragOverlay,\n      ...rest\n    });\n    const animation = dragOverlay.node.animate(animationKeyframes, {\n      duration,\n      easing,\n      fill: 'forwards'\n    });\n    return new Promise(resolve => {\n      animation.onfinish = () => {\n        cleanup == null ? void 0 : cleanup();\n        resolve();\n      };\n    });\n  };\n}\n\nlet key = 0;\nfunction useKey(id) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (id == null) {\n      return;\n    }\n\n    key++;\n    return key;\n  }, [id]);\n}\n\nconst DragOverlay = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().memo(_ref => {\n  let {\n    adjustScale = false,\n    children,\n    dropAnimation: dropAnimationConfig,\n    style,\n    transition,\n    modifiers,\n    wrapperElement = 'div',\n    className,\n    zIndex = 999\n  } = _ref;\n  const {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggableNodes,\n    droppableContainers,\n    dragOverlay,\n    over,\n    measuringConfiguration,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    windowRect\n  } = useDndContext();\n  const transform = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ActiveDraggableContext);\n  const key = useKey(active == null ? void 0 : active.id);\n  const modifiedTransform = applyModifiers(modifiers, {\n    activatorEvent,\n    active,\n    activeNodeRect,\n    containerNodeRect,\n    draggingNodeRect: dragOverlay.rect,\n    over,\n    overlayNodeRect: dragOverlay.rect,\n    scrollableAncestors,\n    scrollableAncestorRects,\n    transform,\n    windowRect\n  });\n  const initialRect = useInitialValue(activeNodeRect);\n  const dropAnimation = useDropAnimation({\n    config: dropAnimationConfig,\n    draggableNodes,\n    droppableContainers,\n    measuringConfiguration\n  }); // We need to wait for the active node to be measured before connecting the drag overlay ref\n  // otherwise collisions can be computed against a mispositioned drag overlay\n\n  const ref = initialRect ? dragOverlay.setRef : undefined;\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(NullifiedContextProvider, null, react__WEBPACK_IMPORTED_MODULE_0___default().createElement(AnimationManager, {\n    animation: dropAnimation\n  }, active && key ? react__WEBPACK_IMPORTED_MODULE_0___default().createElement(PositionedOverlay, {\n    key: key,\n    id: active.id,\n    ref: ref,\n    as: wrapperElement,\n    activatorEvent: activatorEvent,\n    adjustScale: adjustScale,\n    className: className,\n    transition: transition,\n    rect: initialRect,\n    style: {\n      zIndex,\n      ...style\n    },\n    transform: modifiedTransform\n  }, children) : null));\n});\n\n\n//# sourceMappingURL=core.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@dnd-kit/core/dist/core.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@dnd-kit/sortable/dist/sortable.esm.js":
/*!*************************************************************!*\
  !*** ./node_modules/@dnd-kit/sortable/dist/sortable.esm.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SortableContext: () => (/* binding */ SortableContext),\n/* harmony export */   arrayMove: () => (/* binding */ arrayMove),\n/* harmony export */   arraySwap: () => (/* binding */ arraySwap),\n/* harmony export */   defaultAnimateLayoutChanges: () => (/* binding */ defaultAnimateLayoutChanges),\n/* harmony export */   defaultNewIndexGetter: () => (/* binding */ defaultNewIndexGetter),\n/* harmony export */   hasSortableData: () => (/* binding */ hasSortableData),\n/* harmony export */   horizontalListSortingStrategy: () => (/* binding */ horizontalListSortingStrategy),\n/* harmony export */   rectSortingStrategy: () => (/* binding */ rectSortingStrategy),\n/* harmony export */   rectSwappingStrategy: () => (/* binding */ rectSwappingStrategy),\n/* harmony export */   sortableKeyboardCoordinates: () => (/* binding */ sortableKeyboardCoordinates),\n/* harmony export */   useSortable: () => (/* binding */ useSortable),\n/* harmony export */   verticalListSortingStrategy: () => (/* binding */ verticalListSortingStrategy)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @dnd-kit/core */ \"(ssr)/./node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/utilities */ \"(ssr)/./node_modules/@dnd-kit/utilities/dist/utilities.esm.js\");\n\n\n\n\n/**\r\n * Move an array item to a different position. Returns a new array with the item moved to the new position.\r\n */\nfunction arrayMove(array, from, to) {\n  const newArray = array.slice();\n  newArray.splice(to < 0 ? newArray.length + to : to, 0, newArray.splice(from, 1)[0]);\n  return newArray;\n}\n\n/**\r\n * Swap an array item to a different position. Returns a new array with the item swapped to the new position.\r\n */\nfunction arraySwap(array, from, to) {\n  const newArray = array.slice();\n  newArray[from] = array[to];\n  newArray[to] = array[from];\n  return newArray;\n}\n\nfunction getSortedRects(items, rects) {\n  return items.reduce((accumulator, id, index) => {\n    const rect = rects.get(id);\n\n    if (rect) {\n      accumulator[index] = rect;\n    }\n\n    return accumulator;\n  }, Array(items.length));\n}\n\nfunction isValidIndex(index) {\n  return index !== null && index >= 0;\n}\n\nfunction itemsEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction normalizeDisabled(disabled) {\n  if (typeof disabled === 'boolean') {\n    return {\n      draggable: disabled,\n      droppable: disabled\n    };\n  }\n\n  return disabled;\n}\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1\n};\nconst horizontalListSortingStrategy = _ref => {\n  var _rects$activeIndex;\n\n  let {\n    rects,\n    activeNodeRect: fallbackActiveRect,\n    activeIndex,\n    overIndex,\n    index\n  } = _ref;\n  const activeNodeRect = (_rects$activeIndex = rects[activeIndex]) != null ? _rects$activeIndex : fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index === activeIndex) {\n    const newIndexRect = rects[overIndex];\n\n    if (!newIndexRect) {\n      return null;\n    }\n\n    return {\n      x: activeIndex < overIndex ? newIndexRect.left + newIndexRect.width - (activeNodeRect.left + activeNodeRect.width) : newIndexRect.left - activeNodeRect.left,\n      y: 0,\n      ...defaultScale\n    };\n  }\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: -activeNodeRect.width - itemGap,\n      y: 0,\n      ...defaultScale\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: activeNodeRect.width + itemGap,\n      y: 0,\n      ...defaultScale\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale\n  };\n};\n\nfunction getItemGap(rects, index, activeIndex) {\n  const currentRect = rects[index];\n  const previousRect = rects[index - 1];\n  const nextRect = rects[index + 1];\n\n  if (!currentRect || !previousRect && !nextRect) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect ? currentRect.left - (previousRect.left + previousRect.width) : nextRect.left - (currentRect.left + currentRect.width);\n  }\n\n  return nextRect ? nextRect.left - (currentRect.left + currentRect.width) : currentRect.left - (previousRect.left + previousRect.width);\n}\n\nconst rectSortingStrategy = _ref => {\n  let {\n    rects,\n    activeIndex,\n    overIndex,\n    index\n  } = _ref;\n  const newRects = arrayMove(rects, overIndex, activeIndex);\n  const oldRect = rects[index];\n  const newRect = newRects[index];\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height\n  };\n};\n\nconst rectSwappingStrategy = _ref => {\n  let {\n    activeIndex,\n    index,\n    rects,\n    overIndex\n  } = _ref;\n  let oldRect;\n  let newRect;\n\n  if (index === activeIndex) {\n    oldRect = rects[index];\n    newRect = rects[overIndex];\n  }\n\n  if (index === overIndex) {\n    oldRect = rects[index];\n    newRect = rects[activeIndex];\n  }\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height\n  };\n};\n\n// To-do: We should be calculating scale transformation\nconst defaultScale$1 = {\n  scaleX: 1,\n  scaleY: 1\n};\nconst verticalListSortingStrategy = _ref => {\n  var _rects$activeIndex;\n\n  let {\n    activeIndex,\n    activeNodeRect: fallbackActiveRect,\n    index,\n    rects,\n    overIndex\n  } = _ref;\n  const activeNodeRect = (_rects$activeIndex = rects[activeIndex]) != null ? _rects$activeIndex : fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  if (index === activeIndex) {\n    const overIndexRect = rects[overIndex];\n\n    if (!overIndexRect) {\n      return null;\n    }\n\n    return {\n      x: 0,\n      y: activeIndex < overIndex ? overIndexRect.top + overIndexRect.height - (activeNodeRect.top + activeNodeRect.height) : overIndexRect.top - activeNodeRect.top,\n      ...defaultScale$1\n    };\n  }\n\n  const itemGap = getItemGap$1(rects, index, activeIndex);\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: 0,\n      y: -activeNodeRect.height - itemGap,\n      ...defaultScale$1\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: 0,\n      y: activeNodeRect.height + itemGap,\n      ...defaultScale$1\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale$1\n  };\n};\n\nfunction getItemGap$1(clientRects, index, activeIndex) {\n  const currentRect = clientRects[index];\n  const previousRect = clientRects[index - 1];\n  const nextRect = clientRects[index + 1];\n\n  if (!currentRect) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect ? currentRect.top - (previousRect.top + previousRect.height) : nextRect ? nextRect.top - (currentRect.top + currentRect.height) : 0;\n  }\n\n  return nextRect ? nextRect.top - (currentRect.top + currentRect.height) : previousRect ? currentRect.top - (previousRect.top + previousRect.height) : 0;\n}\n\nconst ID_PREFIX = 'Sortable';\nconst Context = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext({\n  activeIndex: -1,\n  containerId: ID_PREFIX,\n  disableTransforms: false,\n  items: [],\n  overIndex: -1,\n  useDragOverlay: false,\n  sortedRects: [],\n  strategy: rectSortingStrategy,\n  disabled: {\n    draggable: false,\n    droppable: false\n  }\n});\nfunction SortableContext(_ref) {\n  let {\n    children,\n    id,\n    items: userDefinedItems,\n    strategy = rectSortingStrategy,\n    disabled: disabledProp = false\n  } = _ref;\n  const {\n    active,\n    dragOverlay,\n    droppableRects,\n    over,\n    measureDroppableContainers\n  } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.useDndContext)();\n  const containerId = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(ID_PREFIX, id);\n  const useDragOverlay = Boolean(dragOverlay.rect !== null);\n  const items = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => userDefinedItems.map(item => typeof item === 'object' && 'id' in item ? item.id : item), [userDefinedItems]);\n  const isDragging = active != null;\n  const activeIndex = active ? items.indexOf(active.id) : -1;\n  const overIndex = over ? items.indexOf(over.id) : -1;\n  const previousItemsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(items);\n  const itemsHaveChanged = !itemsEqual(items, previousItemsRef.current);\n  const disableTransforms = overIndex !== -1 && activeIndex === -1 || itemsHaveChanged;\n  const disabled = normalizeDisabled(disabledProp);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (itemsHaveChanged && isDragging) {\n      measureDroppableContainers(items);\n    }\n  }, [itemsHaveChanged, items, isDragging, measureDroppableContainers]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    previousItemsRef.current = items;\n  }, [items]);\n  const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    activeIndex,\n    containerId,\n    disabled,\n    disableTransforms,\n    items,\n    overIndex,\n    useDragOverlay,\n    sortedRects: getSortedRects(items, droppableRects),\n    strategy\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [activeIndex, containerId, disabled.draggable, disabled.droppable, disableTransforms, items, overIndex, droppableRects, useDragOverlay, strategy]);\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\n\nconst defaultNewIndexGetter = _ref => {\n  let {\n    id,\n    items,\n    activeIndex,\n    overIndex\n  } = _ref;\n  return arrayMove(items, activeIndex, overIndex).indexOf(id);\n};\nconst defaultAnimateLayoutChanges = _ref2 => {\n  let {\n    containerId,\n    isSorting,\n    wasDragging,\n    index,\n    items,\n    newIndex,\n    previousItems,\n    previousContainerId,\n    transition\n  } = _ref2;\n\n  if (!transition || !wasDragging) {\n    return false;\n  }\n\n  if (previousItems !== items && index === newIndex) {\n    return false;\n  }\n\n  if (isSorting) {\n    return true;\n  }\n\n  return newIndex !== index && containerId === previousContainerId;\n};\nconst defaultTransition = {\n  duration: 200,\n  easing: 'ease'\n};\nconst transitionProperty = 'transform';\nconst disabledTransition = /*#__PURE__*/_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transition.toString({\n  property: transitionProperty,\n  duration: 0,\n  easing: 'linear'\n});\nconst defaultAttributes = {\n  roleDescription: 'sortable'\n};\n\n/*\r\n * When the index of an item changes while sorting,\r\n * we need to temporarily disable the transforms\r\n */\n\nfunction useDerivedTransform(_ref) {\n  let {\n    disabled,\n    index,\n    node,\n    rect\n  } = _ref;\n  const [derivedTransform, setDerivedtransform] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const previousIndex = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(index);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (!disabled && index !== previousIndex.current && node.current) {\n      const initial = rect.current;\n\n      if (initial) {\n        const current = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.getClientRect)(node.current, {\n          ignoreTransform: true\n        });\n        const delta = {\n          x: initial.left - current.left,\n          y: initial.top - current.top,\n          scaleX: initial.width / current.width,\n          scaleY: initial.height / current.height\n        };\n\n        if (delta.x || delta.y) {\n          setDerivedtransform(delta);\n        }\n      }\n    }\n\n    if (index !== previousIndex.current) {\n      previousIndex.current = index;\n    }\n  }, [disabled, index, node, rect]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (derivedTransform) {\n      setDerivedtransform(null);\n    }\n  }, [derivedTransform]);\n  return derivedTransform;\n}\n\nfunction useSortable(_ref) {\n  let {\n    animateLayoutChanges = defaultAnimateLayoutChanges,\n    attributes: userDefinedAttributes,\n    disabled: localDisabled,\n    data: customData,\n    getNewIndex = defaultNewIndexGetter,\n    id,\n    strategy: localStrategy,\n    resizeObserverConfig,\n    transition = defaultTransition\n  } = _ref;\n  const {\n    items,\n    containerId,\n    activeIndex,\n    disabled: globalDisabled,\n    disableTransforms,\n    sortedRects,\n    overIndex,\n    useDragOverlay,\n    strategy: globalStrategy\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Context);\n  const disabled = normalizeLocalDisabled(localDisabled, globalDisabled);\n  const index = items.indexOf(id);\n  const data = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    sortable: {\n      containerId,\n      index,\n      items\n    },\n    ...customData\n  }), [containerId, customData, index, items]);\n  const itemsAfterCurrentSortable = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => items.slice(items.indexOf(id)), [items, id]);\n  const {\n    rect,\n    node,\n    isOver,\n    setNodeRef: setDroppableNodeRef\n  } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.useDroppable)({\n    id,\n    data,\n    disabled: disabled.droppable,\n    resizeObserverConfig: {\n      updateMeasurementsFor: itemsAfterCurrentSortable,\n      ...resizeObserverConfig\n    }\n  });\n  const {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes,\n    setNodeRef: setDraggableNodeRef,\n    listeners,\n    isDragging,\n    over,\n    setActivatorNodeRef,\n    transform\n  } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.useDraggable)({\n    id,\n    data,\n    attributes: { ...defaultAttributes,\n      ...userDefinedAttributes\n    },\n    disabled: disabled.draggable\n  });\n  const setNodeRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useCombinedRefs)(setDroppableNodeRef, setDraggableNodeRef);\n  const isSorting = Boolean(active);\n  const displaceItem = isSorting && !disableTransforms && isValidIndex(activeIndex) && isValidIndex(overIndex);\n  const shouldDisplaceDragSource = !useDragOverlay && isDragging;\n  const dragSourceDisplacement = shouldDisplaceDragSource && displaceItem ? transform : null;\n  const strategy = localStrategy != null ? localStrategy : globalStrategy;\n  const finalTransform = displaceItem ? dragSourceDisplacement != null ? dragSourceDisplacement : strategy({\n    rects: sortedRects,\n    activeNodeRect,\n    activeIndex,\n    overIndex,\n    index\n  }) : null;\n  const newIndex = isValidIndex(activeIndex) && isValidIndex(overIndex) ? getNewIndex({\n    id,\n    items,\n    activeIndex,\n    overIndex\n  }) : index;\n  const activeId = active == null ? void 0 : active.id;\n  const previous = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    activeId,\n    items,\n    newIndex,\n    containerId\n  });\n  const itemsHaveChanged = items !== previous.current.items;\n  const shouldAnimateLayoutChanges = animateLayoutChanges({\n    active,\n    containerId,\n    isDragging,\n    isSorting,\n    id,\n    index,\n    items,\n    newIndex: previous.current.newIndex,\n    previousItems: previous.current.items,\n    previousContainerId: previous.current.containerId,\n    transition,\n    wasDragging: previous.current.activeId != null\n  });\n  const derivedTransform = useDerivedTransform({\n    disabled: !shouldAnimateLayoutChanges,\n    index,\n    node,\n    rect\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (isSorting && previous.current.newIndex !== newIndex) {\n      previous.current.newIndex = newIndex;\n    }\n\n    if (containerId !== previous.current.containerId) {\n      previous.current.containerId = containerId;\n    }\n\n    if (items !== previous.current.items) {\n      previous.current.items = items;\n    }\n  }, [isSorting, newIndex, containerId, items]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (activeId === previous.current.activeId) {\n      return;\n    }\n\n    if (activeId != null && previous.current.activeId == null) {\n      previous.current.activeId = activeId;\n      return;\n    }\n\n    const timeoutId = setTimeout(() => {\n      previous.current.activeId = activeId;\n    }, 50);\n    return () => clearTimeout(timeoutId);\n  }, [activeId]);\n  return {\n    active,\n    activeIndex,\n    attributes,\n    data,\n    rect,\n    index,\n    newIndex,\n    items,\n    isOver,\n    isSorting,\n    isDragging,\n    listeners,\n    node,\n    overIndex,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    setDroppableNodeRef,\n    setDraggableNodeRef,\n    transform: derivedTransform != null ? derivedTransform : finalTransform,\n    transition: getTransition()\n  };\n\n  function getTransition() {\n    if ( // Temporarily disable transitions for a single frame to set up derived transforms\n    derivedTransform || // Or to prevent items jumping to back to their \"new\" position when items change\n    itemsHaveChanged && previous.current.newIndex === index) {\n      return disabledTransition;\n    }\n\n    if (shouldDisplaceDragSource && !(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(activatorEvent) || !transition) {\n      return undefined;\n    }\n\n    if (isSorting || shouldAnimateLayoutChanges) {\n      return _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transition.toString({ ...transition,\n        property: transitionProperty\n      });\n    }\n\n    return undefined;\n  }\n}\n\nfunction normalizeLocalDisabled(localDisabled, globalDisabled) {\n  var _localDisabled$dragga, _localDisabled$droppa;\n\n  if (typeof localDisabled === 'boolean') {\n    return {\n      draggable: localDisabled,\n      // Backwards compatibility\n      droppable: false\n    };\n  }\n\n  return {\n    draggable: (_localDisabled$dragga = localDisabled == null ? void 0 : localDisabled.draggable) != null ? _localDisabled$dragga : globalDisabled.draggable,\n    droppable: (_localDisabled$droppa = localDisabled == null ? void 0 : localDisabled.droppable) != null ? _localDisabled$droppa : globalDisabled.droppable\n  };\n}\n\nfunction hasSortableData(entry) {\n  if (!entry) {\n    return false;\n  }\n\n  const data = entry.data.current;\n\n  if (data && 'sortable' in data && typeof data.sortable === 'object' && 'containerId' in data.sortable && 'items' in data.sortable && 'index' in data.sortable) {\n    return true;\n  }\n\n  return false;\n}\n\nconst directions = [_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Down, _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Right, _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Up, _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Left];\nconst sortableKeyboardCoordinates = (event, _ref) => {\n  let {\n    context: {\n      active,\n      collisionRect,\n      droppableRects,\n      droppableContainers,\n      over,\n      scrollableAncestors\n    }\n  } = _ref;\n\n  if (directions.includes(event.code)) {\n    event.preventDefault();\n\n    if (!active || !collisionRect) {\n      return;\n    }\n\n    const filteredContainers = [];\n    droppableContainers.getEnabled().forEach(entry => {\n      if (!entry || entry != null && entry.disabled) {\n        return;\n      }\n\n      const rect = droppableRects.get(entry.id);\n\n      if (!rect) {\n        return;\n      }\n\n      switch (event.code) {\n        case _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Down:\n          if (collisionRect.top < rect.top) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n\n        case _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Up:\n          if (collisionRect.top > rect.top) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n\n        case _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Left:\n          if (collisionRect.left > rect.left) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n\n        case _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Right:\n          if (collisionRect.left < rect.left) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n      }\n    });\n    const collisions = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.closestCorners)({\n      active,\n      collisionRect: collisionRect,\n      droppableRects,\n      droppableContainers: filteredContainers,\n      pointerCoordinates: null\n    });\n    let closestId = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.getFirstCollision)(collisions, 'id');\n\n    if (closestId === (over == null ? void 0 : over.id) && collisions.length > 1) {\n      closestId = collisions[1].id;\n    }\n\n    if (closestId != null) {\n      const activeDroppable = droppableContainers.get(active.id);\n      const newDroppable = droppableContainers.get(closestId);\n      const newRect = newDroppable ? droppableRects.get(newDroppable.id) : null;\n      const newNode = newDroppable == null ? void 0 : newDroppable.node.current;\n\n      if (newNode && newRect && activeDroppable && newDroppable) {\n        const newScrollAncestors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.getScrollableAncestors)(newNode);\n        const hasDifferentScrollAncestors = newScrollAncestors.some((element, index) => scrollableAncestors[index] !== element);\n        const hasSameContainer = isSameContainer(activeDroppable, newDroppable);\n        const isAfterActive = isAfter(activeDroppable, newDroppable);\n        const offset = hasDifferentScrollAncestors || !hasSameContainer ? {\n          x: 0,\n          y: 0\n        } : {\n          x: isAfterActive ? collisionRect.width - newRect.width : 0,\n          y: isAfterActive ? collisionRect.height - newRect.height : 0\n        };\n        const rectCoordinates = {\n          x: newRect.left,\n          y: newRect.top\n        };\n        const newCoordinates = offset.x && offset.y ? rectCoordinates : (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(rectCoordinates, offset);\n        return newCoordinates;\n      }\n    }\n  }\n\n  return undefined;\n};\n\nfunction isSameContainer(a, b) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  return a.data.current.sortable.containerId === b.data.current.sortable.containerId;\n}\n\nfunction isAfter(a, b) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  if (!isSameContainer(a, b)) {\n    return false;\n  }\n\n  return a.data.current.sortable.index < b.data.current.sortable.index;\n}\n\n\n//# sourceMappingURL=sortable.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@dnd-kit/sortable/dist/sortable.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@dnd-kit/utilities/dist/utilities.esm.js":
/*!***************************************************************!*\
  !*** ./node_modules/@dnd-kit/utilities/dist/utilities.esm.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSS: () => (/* binding */ CSS),\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   canUseDOM: () => (/* binding */ canUseDOM),\n/* harmony export */   findFirstFocusableNode: () => (/* binding */ findFirstFocusableNode),\n/* harmony export */   getEventCoordinates: () => (/* binding */ getEventCoordinates),\n/* harmony export */   getOwnerDocument: () => (/* binding */ getOwnerDocument),\n/* harmony export */   getWindow: () => (/* binding */ getWindow),\n/* harmony export */   hasViewportRelativeCoordinates: () => (/* binding */ hasViewportRelativeCoordinates),\n/* harmony export */   isDocument: () => (/* binding */ isDocument),\n/* harmony export */   isHTMLElement: () => (/* binding */ isHTMLElement),\n/* harmony export */   isKeyboardEvent: () => (/* binding */ isKeyboardEvent),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   isSVGElement: () => (/* binding */ isSVGElement),\n/* harmony export */   isTouchEvent: () => (/* binding */ isTouchEvent),\n/* harmony export */   isWindow: () => (/* binding */ isWindow),\n/* harmony export */   subtract: () => (/* binding */ subtract),\n/* harmony export */   useCombinedRefs: () => (/* binding */ useCombinedRefs),\n/* harmony export */   useEvent: () => (/* binding */ useEvent),\n/* harmony export */   useInterval: () => (/* binding */ useInterval),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   useLatestValue: () => (/* binding */ useLatestValue),\n/* harmony export */   useLazyMemo: () => (/* binding */ useLazyMemo),\n/* harmony export */   useNodeRef: () => (/* binding */ useNodeRef),\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious),\n/* harmony export */   useUniqueId: () => (/* binding */ useUniqueId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nfunction useCombinedRefs() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => node => {\n    refs.forEach(ref => ref(node));\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  refs);\n}\n\n// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nconst canUseDOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\nfunction isWindow(element) {\n  const elementString = Object.prototype.toString.call(element);\n  return elementString === '[object Window]' || // In Electron context the Window object serializes to [object global]\n  elementString === '[object global]';\n}\n\nfunction isNode(node) {\n  return 'nodeType' in node;\n}\n\nfunction getWindow(target) {\n  var _target$ownerDocument, _target$ownerDocument2;\n\n  if (!target) {\n    return window;\n  }\n\n  if (isWindow(target)) {\n    return target;\n  }\n\n  if (!isNode(target)) {\n    return window;\n  }\n\n  return (_target$ownerDocument = (_target$ownerDocument2 = target.ownerDocument) == null ? void 0 : _target$ownerDocument2.defaultView) != null ? _target$ownerDocument : window;\n}\n\nfunction isDocument(node) {\n  const {\n    Document\n  } = getWindow(node);\n  return node instanceof Document;\n}\n\nfunction isHTMLElement(node) {\n  if (isWindow(node)) {\n    return false;\n  }\n\n  return node instanceof getWindow(node).HTMLElement;\n}\n\nfunction isSVGElement(node) {\n  return node instanceof getWindow(node).SVGElement;\n}\n\nfunction getOwnerDocument(target) {\n  if (!target) {\n    return document;\n  }\n\n  if (isWindow(target)) {\n    return target.document;\n  }\n\n  if (!isNode(target)) {\n    return document;\n  }\n\n  if (isDocument(target)) {\n    return target;\n  }\n\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n\n  return document;\n}\n\n/**\r\n * A hook that resolves to useEffect on the server and useLayoutEffect on the client\r\n * @param callback {function} Callback function that is invoked when the dependencies of the hook change\r\n */\n\nconst useIsomorphicLayoutEffect = canUseDOM ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\nfunction useEvent(handler) {\n  const handlerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(handler);\n  useIsomorphicLayoutEffect(() => {\n    handlerRef.current = handler;\n  });\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return handlerRef.current == null ? void 0 : handlerRef.current(...args);\n  }, []);\n}\n\nfunction useInterval() {\n  const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const set = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((listener, duration) => {\n    intervalRef.current = setInterval(listener, duration);\n  }, []);\n  const clear = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (intervalRef.current !== null) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n  return [set, clear];\n}\n\nfunction useLatestValue(value, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [value];\n  }\n\n  const valueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);\n  useIsomorphicLayoutEffect(() => {\n    if (valueRef.current !== value) {\n      valueRef.current = value;\n    }\n  }, dependencies);\n  return valueRef;\n}\n\nfunction useLazyMemo(callback, dependencies) {\n  const valueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const newValue = callback(valueRef.current);\n    valueRef.current = newValue;\n    return newValue;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...dependencies]);\n}\n\nfunction useNodeRef(onChange) {\n  const onChangeHandler = useEvent(onChange);\n  const node = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const setNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(element => {\n    if (element !== node.current) {\n      onChangeHandler == null ? void 0 : onChangeHandler(element, node.current);\n    }\n\n    node.current = element;\n  }, //eslint-disable-next-line\n  []);\n  return [node, setNodeRef];\n}\n\nfunction usePrevious(value) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ref.current = value;\n  }, [value]);\n  return ref.current;\n}\n\nlet ids = {};\nfunction useUniqueId(prefix, value) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (value) {\n      return value;\n    }\n\n    const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n    ids[prefix] = id;\n    return prefix + \"-\" + id;\n  }, [prefix, value]);\n}\n\nfunction createAdjustmentFn(modifier) {\n  return function (object) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n\n    return adjustments.reduce((accumulator, adjustment) => {\n      const entries = Object.entries(adjustment);\n\n      for (const [key, valueAdjustment] of entries) {\n        const value = accumulator[key];\n\n        if (value != null) {\n          accumulator[key] = value + modifier * valueAdjustment;\n        }\n      }\n\n      return accumulator;\n    }, { ...object\n    });\n  };\n}\n\nconst add = /*#__PURE__*/createAdjustmentFn(1);\nconst subtract = /*#__PURE__*/createAdjustmentFn(-1);\n\nfunction hasViewportRelativeCoordinates(event) {\n  return 'clientX' in event && 'clientY' in event;\n}\n\nfunction isKeyboardEvent(event) {\n  if (!event) {\n    return false;\n  }\n\n  const {\n    KeyboardEvent\n  } = getWindow(event.target);\n  return KeyboardEvent && event instanceof KeyboardEvent;\n}\n\nfunction isTouchEvent(event) {\n  if (!event) {\n    return false;\n  }\n\n  const {\n    TouchEvent\n  } = getWindow(event.target);\n  return TouchEvent && event instanceof TouchEvent;\n}\n\n/**\r\n * Returns the normalized x and y coordinates for mouse and touch events.\r\n */\n\nfunction getEventCoordinates(event) {\n  if (isTouchEvent(event)) {\n    if (event.touches && event.touches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.touches[0];\n      return {\n        x,\n        y\n      };\n    } else if (event.changedTouches && event.changedTouches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.changedTouches[0];\n      return {\n        x,\n        y\n      };\n    }\n  }\n\n  if (hasViewportRelativeCoordinates(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n\n  return null;\n}\n\nconst CSS = /*#__PURE__*/Object.freeze({\n  Translate: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      const {\n        x,\n        y\n      } = transform;\n      return \"translate3d(\" + (x ? Math.round(x) : 0) + \"px, \" + (y ? Math.round(y) : 0) + \"px, 0)\";\n    }\n\n  },\n  Scale: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      const {\n        scaleX,\n        scaleY\n      } = transform;\n      return \"scaleX(\" + scaleX + \") scaleY(\" + scaleY + \")\";\n    }\n\n  },\n  Transform: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      return [CSS.Translate.toString(transform), CSS.Scale.toString(transform)].join(' ');\n    }\n\n  },\n  Transition: {\n    toString(_ref) {\n      let {\n        property,\n        duration,\n        easing\n      } = _ref;\n      return property + \" \" + duration + \"ms \" + easing;\n    }\n\n  }\n});\n\nconst SELECTOR = 'a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]';\nfunction findFirstFocusableNode(element) {\n  if (element.matches(SELECTOR)) {\n    return element;\n  }\n\n  return element.querySelector(SELECTOR);\n}\n\n\n//# sourceMappingURL=utilities.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@dnd-kit/utilities/dist/utilities.esm.js\n");

/***/ })

};
;